{"name": "expert-wonders-nextjs", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "api": "cd api && npm install && npm start", "api:dev": "cd api && npm install && npm run dev", "pm2:start": "pm2 start ecosystem.config.cjs --env production", "pm2:stop": "pm2 stop all", "pm2:restart": "pm2 restart all", "pm2:status": "pm2 list", "pm2:logs": "pm2 logs", "pm2:save": "pm2 save", "deploy": "./pm2-management.sh restart && ./pm2-management.sh save"}, "dependencies": {"@types/multer": "^1.4.12", "framer-motion": "^12.20.1", "multer": "^1.4.5-lts.1", "next": "^15.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "remixicon": "^4.6.0", "sharp": "^0.33.5", "sqlite3": "^5.1.6", "swiper": "^11.2.10"}, "devDependencies": {"@types/node": "^22.10.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "^15.1.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.3", "typescript": "^5.7.3"}}