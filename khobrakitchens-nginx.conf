# Nginx Configuration for Expert Wonders Next.js (عجائب الخبراء)
# Domain: khobrakitchens.com
# Server IP: ************

# إعادة توجيه www إلى non-www (HTTP)
server {
    listen 80;
    listen [::]:80;
    server_name www.khobrakitchens.com;
    return 301 https://khobrakitchens.com$request_uri;
}

# إعادة التوجيه من HTTP إلى HTTPS (non-www)
server {
    listen 80;
    listen [::]:80;
    server_name khobrakitchens.com ************;
    return 301 https://khobrakitchens.com$request_uri;
}

# إعادة توجيه www إلى non-www (HTTPS)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name www.khobrakitchens.com;

    # SSL Configuration - Let's Encrypt certificates
    ssl_certificate /etc/letsencrypt/live/khobrakitchens.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/khobrakitchens.com/privkey.pem;

    return 301 https://khobrakitchens.com$request_uri;
}

# السيرفر الرئيسي عبر HTTPS (non-www only)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name khobrakitchens.com;

    # SSL Configuration - Let's Encrypt certificates
    ssl_certificate /etc/letsencrypt/live/khobrakitchens.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/khobrakitchens.com/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Root directory for Next.js
    root /var/www/html;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https: http:; connect-src 'self' https://www.google-analytics.com; frame-src 'self' https://www.youtube.com;" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Block access to sensitive directories
    location ~ ^/(database|api/legacy)/ {
        deny all;
        return 403;
    }

    # Block access to sensitive files
    location ~ /\.(env|db|git|svn|htaccess|htpasswd)$ {
        deny all;
        return 403;
    }

    # Serve Next.js static files with long cache
    location /_next/static/ {
        alias /var/www/html/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }

    # Serve uploaded images with cache
    location /uploads/ {
        alias /var/www/html/public/uploads/;
        expires 1y;
        add_header Cache-Control "public";
        add_header X-Content-Type-Options nosniff;

        # Only allow image files
        location ~* \.(jpg|jpeg|png|gif|webp|svg)$ {
            try_files $uri =404;
        }

        # Block other file types
        location ~ {
            return 403;
        }
    }

    # Serve other static files
    location /favicon.ico {
        alias /var/www/html/public/favicon.ico;
        expires 1y;
        add_header Cache-Control "public";
    }

    location /robots.txt {
        alias /var/www/html/public/robots.txt;
        expires 1d;
        add_header Cache-Control "public";
    }

    location /sitemap.xml {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        expires 1d;
        add_header Cache-Control "public";
    }

    # Admin panel
    location /admin {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # API routes
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;

        # Increase body size for file uploads
        client_max_body_size 10M;
    }

    # Main Next.js application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;

        # Handle Next.js routing
        try_files $uri $uri/ @nextjs;
    }

    # Fallback for Next.js routing
    location @nextjs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # Logging
    access_log /var/log/nginx/khobrakitchens.com.access.log;
    error_log /var/log/nginx/khobrakitchens.com.error.log;
}
