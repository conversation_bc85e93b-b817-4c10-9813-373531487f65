import type { Metadata } from 'next'
import { Taja<PERSON> } from 'next/font/google'
import './globals.css'
import MobileDetector from '@/components/mobile/MobileDetector'

const tajawal = Tajawal({
  subsets: ['arabic', 'latin'],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',
    template: '%s | عجائب الخبراء'
  },
  description: 'شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية والفاخرة بأعلى معايير الجودة والحرفية في المملكة العربية السعودية. احصل على مطبخ أحلامك اليوم!',
  keywords: ['مطابخ السعودية', 'تصميم مطابخ', 'مطابخ عصرية', 'مطابخ كلاسيكية', 'خزائن ملابس', 'تفصيل مطابخ', 'مطابخ فاخرة', 'عجائب الخبراء', 'مطابخ الرياض'],
  authors: [{ name: 'عجائب الخبراء' }],
  creator: 'عجائب الخبراء',
  publisher: 'عجائب الخبراء',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://khobrakitchens.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',
    description: 'شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية والفاخرة بأعلى معايير الجودة والحرفية في المملكة العربية السعودية.',
    url: 'https://khobrakitchens.com',
    siteName: 'عجائب الخبراء',
    locale: 'ar_SA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',
    description: 'شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية والفاخرة بأعلى معايير الجودة والحرفية في المملكة العربية السعودية.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className={tajawal.variable}>
      <body className={`${tajawal.className} antialiased bg-gray-50`}>
        <MobileDetector>
          {children}
        </MobileDetector>
      </body>
    </html>
  )
}
