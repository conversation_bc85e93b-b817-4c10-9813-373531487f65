import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'من نحن - عجائب الخبراء',
  description: 'تعرف على شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية في المملكة العربية السعودية',
  keywords: ['عجائب الخبراء', 'من نحن', 'شركة مطابخ', 'تصميم مطابخ السعودية', 'خبرة مطابخ'],
  alternates: {
    canonical: '/about',
  },
  openGraph: {
    title: 'من نحن - عجائب الخبراء',
    description: 'تعرف على شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية في المملكة العربية السعودية',
    url: 'https://khobrakitchens.com/about',
    type: 'website',
  },
}

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
