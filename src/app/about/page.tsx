'use client'

import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import Link from 'next/link'
import { useSocialMedia } from '@/hooks/useSocialMedia'

export default function AboutPage() {
  const { getWhatsAppLink, getPhoneNumber } = useSocialMedia()
  return (
    <div className="bg-gray-50" dir="rtl">
      <Navbar />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              من نحن
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية بأعلى معايير الجودة والحرفية
            </p>
          </div>
        </section>

        {/* Company Story */}
        <section className="py-20 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  قصتنا
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed mb-6">
                  بدأت رحلة عجائب الخبراء منذ أكثر من 15 عاماً برؤية واضحة: تحويل المطابخ والخزائن من مجرد مساحات وظيفية إلى تحف فنية تجمع بين الجمال والعملية.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed mb-6">
                  نحن نؤمن بأن المطبخ هو قلب المنزل، والخزائن هي روح التنظيم. لذلك نسعى دائماً لتقديم حلول مبتكرة تلبي احتياجات عملائنا وتفوق توقعاتهم.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed">
                  اليوم، نفخر بكوننا واحدة من الشركات الرائدة في المملكة العربية السعودية، مع فريق من أمهر الحرفيين والمصممين المتخصصين.
                </p>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl p-8 h-96 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center mx-auto mb-6">
                      <i className="ri-building-line text-white text-4xl"></i>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">15+ سنة</h3>
                    <p className="text-gray-600">من الخبرة والتميز</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Vision & Mission */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                رؤيتنا ورسالتنا
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                نسعى لأن نكون الخيار الأول في تصميم وتنفيذ المطابخ والخزائن في المملكة
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Vision */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mr-4">
                    <i className="ri-eye-line text-white text-2xl"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">رؤيتنا</h3>
                </div>
                <p className="text-gray-700 leading-relaxed text-lg">
                  أن نكون الشركة الرائدة في تصميم وتنفيذ المطابخ والخزائن في المملكة العربية السعودية، ونساهم في تحسين جودة الحياة من خلال إبداعاتنا التي تجمع بين الجمال والوظيفة والجودة العالية.
                </p>
              </div>

              {/* Mission */}
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                    <i className="ri-target-line text-white text-2xl"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">رسالتنا</h3>
                </div>
                <p className="text-gray-700 leading-relaxed text-lg">
                  نلتزم بتقديم خدمات متميزة في تصميم وتنفيذ المطابخ والخزائن باستخدام أحدث التقنيات وأفضل المواد، مع ضمان رضا العملاء والالتزام بالمواعيد والجودة العالية في كل مشروع.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-20 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                قيمنا
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                القيم التي نؤمن بها وتوجه عملنا في كل مشروع
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: 'ri-award-line',
                  title: 'الجودة',
                  description: 'نستخدم أفضل المواد والخامات لضمان جودة عالية ومتانة طويلة الأمد',
                  color: 'from-blue-500 to-blue-600'
                },
                {
                  icon: 'ri-customer-service-line',
                  title: 'خدمة العملاء',
                  description: 'نضع رضا العملاء في المقدمة ونسعى لتجاوز توقعاتهم',
                  color: 'from-green-500 to-green-600'
                },
                {
                  icon: 'ri-lightbulb-line',
                  title: 'الإبداع',
                  description: 'نقدم تصاميم مبتكرة وحلول عملية تناسب احتياجات كل عميل',
                  color: 'from-orange-500 to-orange-600'
                },
                {
                  icon: 'ri-shield-check-line',
                  title: 'الثقة',
                  description: 'نبني علاقات طويلة الأمد مع عملائنا قائمة على الثقة والشفافية',
                  color: 'from-purple-500 to-purple-600'
                }
              ].map((value, index) => (
                <div key={index} className="text-center group">
                  <div className={`w-20 h-20 bg-gradient-to-br ${value.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${value.icon} text-white text-3xl`}></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team */}
        <section className="py-20 bg-gradient-to-br from-primary-50 to-blue-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                فريق العمل
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                فريق من المتخصصين والحرفيين المهرة يعملون بشغف لتحقيق أحلامكم
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: 'ri-pencil-ruler-line',
                  title: 'فريق التصميم',
                  description: 'مصممون محترفون متخصصون في إبداع تصاميم عصرية وكلاسيكية تناسب جميع الأذواق',
                  count: '10+'
                },
                {
                  icon: 'ri-hammer-line',
                  title: 'فريق التنفيذ',
                  description: 'حرفيون مهرة يتمتعون بخبرة واسعة في تنفيذ المشاريع بأعلى معايير الجودة',
                  count: '25+'
                },
                {
                  icon: 'ri-customer-service-2-line',
                  title: 'فريق خدمة العملاء',
                  description: 'متخصصون في خدمة العملاء يضمنون تجربة مميزة من البداية حتى النهاية',
                  count: '5+'
                }
              ].map((team, index) => (
                <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <i className={`${team.icon} text-white text-3xl`}></i>
                  </div>
                  <div className="text-3xl font-bold text-primary-600 mb-2">{team.count}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{team.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{team.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-primary-600 to-primary-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              جاهز لبدء مشروعك معنا؟
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              تواصل معنا اليوم واحصل على استشارة مجانية وعرض سعر مخصص لمشروعك
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href={getWhatsAppLink()}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center"
              >
                <i className="ri-whatsapp-line text-xl"></i>
                تواصل عبر واتساب
              </Link>

              <Link
                href={`tel:${getPhoneNumber()}`}
                className="bg-white text-primary-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center"
              >
                <i className="ri-phone-line text-xl"></i>
                اتصل بنا الآن
              </Link>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
