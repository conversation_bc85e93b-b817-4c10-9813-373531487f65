@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts - <PERSON><PERSON>wal */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* Remix Icons */
@import 'remixicon/fonts/remixicon.css';

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Ta<PERSON>wal', Arial, sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
*:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Button focus styles */
button:focus,
a:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Animation classes */
@layer components {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.5s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
}

/* Loading spinner */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0ea5e9;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.5);
}

/* Swiper customization */
.swiper-pagination-bullet {
  background: #0ea5e9 !important;
  opacity: 0.5 !important;
}

.swiper-pagination-bullet-active {
  opacity: 1 !important;
}

.swiper-button-next,
.swiper-button-prev {
  color: #0ea5e9 !important;
}

/* Responsive text */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 0.875rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .responsive-text {
    font-size: 1rem;
  }
}

@media (min-width: 1025px) {
  .responsive-text {
    font-size: 1.125rem;
  }
}
