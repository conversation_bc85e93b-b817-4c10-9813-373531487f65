'use client'

import { useState } from 'react'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import Link from 'next/link'
import { useSocialMedia } from '@/hooks/useSocialMedia'

export default function ContactPage() {
  const { getWhatsAppLink, getPhoneNumber } = useSocialMedia()

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    subject: '',
    message: '',
    projectType: 'kitchen'
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      // هنا يمكن إضافة منطق إرسال النموذج إلى API
      await new Promise(resolve => setTimeout(resolve, 1000)) // محاكاة الإرسال
      
      setSubmitStatus('success')
      setFormData({
        name: '',
        phone: '',
        email: '',
        subject: '',
        message: '',
        projectType: 'kitchen'
      })
    } catch (error) {
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-gray-50" dir="rtl">
      <Navbar />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              تواصل معنا
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              نحن هنا لمساعدتك في تحويل أحلامك إلى واقع. تواصل معنا للحصول على استشارة مجانية
            </p>
          </div>
        </section>

        {/* Contact Info Cards */}
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: 'ri-phone-line',
                  title: 'اتصل بنا',
                  info: getPhoneNumber(),
                  action: `tel:${getPhoneNumber()}`,
                  color: 'from-blue-500 to-blue-600'
                },
                {
                  icon: 'ri-whatsapp-line',
                  title: 'واتساب',
                  info: getPhoneNumber(),
                  action: getWhatsAppLink(),
                  color: 'from-green-500 to-green-600'
                },
                {
                  icon: 'ri-mail-line',
                  title: 'البريد الإلكتروني',
                  info: '<EMAIL>',
                  action: 'mailto:<EMAIL>',
                  color: 'from-purple-500 to-purple-600'
                },
                {
                  icon: 'ri-map-pin-line',
                  title: 'العنوان',
                  info: 'الرياض، المملكة العربية السعودية',
                  action: '#map',
                  color: 'from-orange-500 to-orange-600'
                }
              ].map((contact, index) => (
                <Link
                  key={index}
                  href={contact.action}
                  target={contact.action.startsWith('http') ? '_blank' : undefined}
                  rel={contact.action.startsWith('http') ? 'noopener noreferrer' : undefined}
                  className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 text-center group"
                >
                  <div className={`w-16 h-16 bg-gradient-to-br ${contact.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${contact.icon} text-white text-2xl`}></i>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{contact.title}</h3>
                  <p className="text-gray-600 text-sm">{contact.info}</p>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Form & Map */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              
              {/* Contact Form */}
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  أرسل لنا رسالة
                </h2>
                
                {submitStatus === 'success' && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                      <i className="ri-check-circle-line text-green-500 text-xl mr-3"></i>
                      <p className="text-green-700">تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.</p>
                    </div>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center">
                      <i className="ri-error-warning-line text-red-500 text-xl mr-3"></i>
                      <p className="text-red-700">حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.</p>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        الاسم الكامل *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                        placeholder="أدخل اسمك الكامل"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف *
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                        placeholder="05xxxxxxxx"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label htmlFor="projectType" className="block text-sm font-medium text-gray-700 mb-2">
                      نوع المشروع *
                    </label>
                    <select
                      id="projectType"
                      name="projectType"
                      value={formData.projectType}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                    >
                      <option value="kitchen">مطبخ</option>
                      <option value="cabinet">خزانة</option>
                      <option value="both">مطبخ وخزانة</option>
                      <option value="consultation">استشارة</option>
                      <option value="other">أخرى</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      الموضوع
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                      placeholder="موضوع الرسالة"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      الرسالة *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
                      placeholder="اكتب رسالتك هنا..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-4 px-6 rounded-lg font-bold text-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-3"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <i className="ri-send-plane-line text-xl"></i>
                        إرسال الرسالة
                      </>
                    )}
                  </button>
                </form>
              </div>

              {/* Map */}
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  موقعنا على الخريطة
                </h2>
                
                <div id="map" className="w-full h-96 bg-gray-200 rounded-lg overflow-hidden">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.2!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f03890d489399%3A0xba974d1c98e79fd5!2sRiyadh%20Saudi%20Arabia!5e0!3m2!1sen!2s!4v1640000000000!5m2!1sen!2s"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="موقع عجائب الخبراء"
                  ></iframe>
                </div>

                <div className="mt-6 space-y-4">
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <i className="ri-map-pin-line text-primary-500 text-xl mt-1"></i>
                    <div>
                      <h4 className="font-semibold text-gray-900">العنوان</h4>
                      <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <i className="ri-time-line text-primary-500 text-xl mt-1"></i>
                    <div>
                      <h4 className="font-semibold text-gray-900">ساعات العمل</h4>
                      <p className="text-gray-600">السبت - الخميس: 8:00 ص - 6:00 م</p>
                      <p className="text-gray-600">الجمعة: مغلق</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Contact */}
        <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              تحتاج مساعدة فورية؟
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              تواصل معنا مباشرة للحصول على رد سريع واستشارة فورية
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href={getWhatsAppLink()}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center"
              >
                <i className="ri-whatsapp-line text-xl"></i>
                واتساب فوري
              </Link>

              <Link
                href={`tel:${getPhoneNumber()}`}
                className="bg-white text-primary-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center gap-3 justify-center"
              >
                <i className="ri-phone-line text-xl"></i>
                اتصال مباشر
              </Link>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
