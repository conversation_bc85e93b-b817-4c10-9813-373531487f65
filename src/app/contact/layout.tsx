import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'تواصل معنا - عجائب الخبراء',
  description: 'تواصل مع شركة عجائب الخبراء للحصول على استشارة مجانية وعرض سعر مخصص لمشروع مطبخك أو خزانتك. نحن هنا لمساعدتك',
  keywords: ['تواصل معنا', 'عجائب الخبراء', 'استشارة مطابخ', 'عرض سعر مطبخ', 'خدمة عملاء'],
  alternates: {
    canonical: '/contact',
  },
  openGraph: {
    title: 'تواصل معنا - عجائب الخبراء',
    description: 'تواصل مع شركة عجائب الخبراء للحصول على استشارة مجانية وعرض سعر مخصص لمشروع مطبخك أو خزانتك',
    url: 'https://khobrakitchens.com/contact',
    type: 'website',
  },
}

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
