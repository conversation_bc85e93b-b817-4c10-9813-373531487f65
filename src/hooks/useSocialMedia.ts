'use client'

import { useState, useEffect } from 'react'

interface SocialMedia {
  id: number
  platform: string
  url: string
  icon: string
}

interface ContactInfo {
  id: number
  icon: string
  text: string
  type: string
}

interface FooterData {
  socialMedia: SocialMedia[]
  contactInfo: ContactInfo[]
}

export const useSocialMedia = () => {
  const [socialMedia, setSocialMedia] = useState<SocialMedia[]>([])
  const [contactInfo, setContactInfo] = useState<ContactInfo[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSocialMedia = async () => {
      try {
        const response = await fetch('/api/footer')
        if (response.ok) {
          const data: FooterData = await response.json()
          setSocialMedia(data.socialMedia || [])
          setContactInfo(data.contactInfo || [])
        }
      } catch (error) {
        console.error('Error fetching social media data:', error)
        // Fallback data
        setSocialMedia([
          { id: 1, platform: 'واتساب', url: 'https://wa.me/966557611105', icon: 'ri-whatsapp-line' },
          { id: 2, platform: 'تويتر', url: 'https://twitter.com', icon: 'ri-twitter-x-line' },
          { id: 3, platform: 'إنستغرام', url: 'https://instagram.com', icon: 'ri-instagram-line' },
          { id: 4, platform: 'فيسبوك', url: 'https://facebook.com', icon: 'ri-facebook-line' },
          { id: 5, platform: 'سناب شات', url: 'https://snapchat.com', icon: 'ri-snapchat-line' },
          { id: 6, platform: 'تيك توك', url: 'https://tiktok.com', icon: 'ri-tiktok-line' },
        ])
        setContactInfo([
          { id: 1, icon: 'ri-phone-line', text: '+966 55 761 1105', type: 'phone' },
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchSocialMedia()
  }, [])

  // Helper functions to get specific social media links
  const getWhatsAppLink = (message?: string) => {
    const whatsapp = socialMedia.find(social => 
      social.platform.toLowerCase().includes('واتساب') || 
      social.platform.toLowerCase().includes('whatsapp')
    )
    if (whatsapp) {
      const baseUrl = whatsapp.url.includes('wa.me') ? whatsapp.url : `https://wa.me/966557611105`
      return message ? `${baseUrl}?text=${encodeURIComponent(message)}` : baseUrl
    }
    return `https://wa.me/966557611105${message ? `?text=${encodeURIComponent(message)}` : ''}`
  }

  const getPhoneNumber = () => {
    const phone = contactInfo.find(contact => contact.type === 'phone')
    return phone ? phone.text.replace(/\s/g, '') : '+966557611105'
  }

  const getSocialMediaLink = (platform: string) => {
    const platformLower = platform.toLowerCase()
    const social = socialMedia.find(s => {
      const sPlatformLower = s.platform.toLowerCase()
      return sPlatformLower.includes(platformLower) ||
             platformLower.includes(sPlatformLower) ||
             (platformLower.includes('تويتر') && sPlatformLower.includes('twitter')) ||
             (platformLower.includes('twitter') && sPlatformLower.includes('تويتر')) ||
             (platformLower.includes('إنستغرام') && sPlatformLower.includes('instagram')) ||
             (platformLower.includes('instagram') && sPlatformLower.includes('إنستغرام')) ||
             (platformLower.includes('فيسبوك') && sPlatformLower.includes('facebook')) ||
             (platformLower.includes('facebook') && sPlatformLower.includes('فيسبوك')) ||
             (platformLower.includes('سناب') && sPlatformLower.includes('snap')) ||
             (platformLower.includes('snap') && sPlatformLower.includes('سناب')) ||
             (platformLower.includes('تيك') && sPlatformLower.includes('tik')) ||
             (platformLower.includes('tik') && sPlatformLower.includes('تيك'))
    })
    return social?.url || '#'
  }

  const getSocialMediaIcon = (platform: string) => {
    const platformLower = platform.toLowerCase()
    const social = socialMedia.find(s => {
      const sPlatformLower = s.platform.toLowerCase()
      return sPlatformLower.includes(platformLower) ||
             platformLower.includes(sPlatformLower) ||
             (platformLower.includes('تويتر') && sPlatformLower.includes('twitter')) ||
             (platformLower.includes('twitter') && sPlatformLower.includes('تويتر')) ||
             (platformLower.includes('إنستغرام') && sPlatformLower.includes('instagram')) ||
             (platformLower.includes('instagram') && sPlatformLower.includes('إنستغرام')) ||
             (platformLower.includes('فيسبوك') && sPlatformLower.includes('facebook')) ||
             (platformLower.includes('facebook') && sPlatformLower.includes('فيسبوك')) ||
             (platformLower.includes('سناب') && sPlatformLower.includes('snap')) ||
             (platformLower.includes('snap') && sPlatformLower.includes('سناب')) ||
             (platformLower.includes('تيك') && sPlatformLower.includes('tik')) ||
             (platformLower.includes('tik') && sPlatformLower.includes('تيك'))
    })
    return social?.icon || 'ri-link'
  }

  return {
    socialMedia,
    contactInfo,
    loading,
    getWhatsAppLink,
    getPhoneNumber,
    getSocialMediaLink,
    getSocialMediaIcon
  }
}
