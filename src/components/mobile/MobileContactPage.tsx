'use client'

import { useState } from 'react'
import { useSocialMedia } from '@/hooks/useSocialMedia'

export default function MobileContactPage() {
  const { getWhatsAppLink, getPhoneNumber, getSocialMediaLink, getSocialMediaIcon } = useSocialMedia()
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    service: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const services = [
    'تصميم مطابخ عصرية',
    'تصميم مطابخ كلاسيكية',
    'تصميم خزانات ملابس',
    'تصميم خزانات مطبخ',
    'استشارة تصميم',
    'صيانة وتجديد',
    'أخرى'
  ]

  const contactMethods = [
    {
      icon: 'ri-whatsapp-line',
      title: 'واتساب',
      value: getPhoneNumber(),
      action: () => window.open(getWhatsAppLink(), '_blank'),
      color: 'from-green-500 to-green-600'
    },
    {
      icon: 'ri-phone-line',
      title: 'اتصال مباشر',
      value: getPhoneNumber(),
      action: () => window.open(`tel:${getPhoneNumber()}`, '_self'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: 'ri-mail-line',
      title: 'البريد الإلكتروني',
      value: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>', '_self'),
      color: 'from-purple-500 to-purple-600'
    }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        setSubmitStatus('success')
        setFormData({
          name: '',
          phone: '',
          email: '',
          service: '',
          message: ''
        })
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-kitchen-600 to-kitchen-800 pt-8 pb-6 px-4">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-2">تواصل معنا</h1>
          <p className="text-kitchen-100 text-sm">نحن هنا لمساعدتك في تحقيق حلمك</p>
        </div>
      </div>

      {/* Quick Contact Methods */}
      <div className="px-4 -mt-3 mb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <h2 className="text-base font-bold text-gray-900 mb-3 text-center">طرق التواصل السريع</h2>
          <div className="space-y-2">
            {contactMethods.map((method, index) => (
              <button
                key={index}
                onClick={method.action}
                className={`w-full bg-gradient-to-r ${method.color} text-white p-3 rounded-lg flex items-center justify-between hover:shadow-lg transition-all duration-300`}
              >
                <div className="flex items-center">
                  <i className={`${method.icon} text-lg ml-2`}></i>
                  <div className="text-right">
                    <div className="font-bold text-sm">{method.title}</div>
                    <div className="text-xs opacity-90">{method.value}</div>
                  </div>
                </div>
                <i className="ri-arrow-left-s-line text-lg"></i>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Contact Form */}
      <div className="px-4 mb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <h2 className="text-base font-bold text-gray-900 mb-4 text-center">أرسل لنا رسالة</h2>
          
          <form onSubmit={handleSubmit} className="space-y-3">
            <div>
              <label className="block text-gray-700 font-medium mb-1 text-sm">الاسم الكامل *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm"
                placeholder="أدخل اسمك الكامل"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-1 text-sm">رقم الهاتف *</label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm"
                placeholder="05xxxxxxxx"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-1 text-sm">البريد الإلكتروني</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-1 text-sm">نوع الخدمة المطلوبة</label>
              <select
                name="service"
                value={formData.service}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm"
              >
                <option value="">اختر نوع الخدمة</option>
                {services.map((service, index) => (
                  <option key={index} value={service}>{service}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-1 text-sm">الرسالة *</label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                required
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 resize-none text-sm"
                placeholder="اكتب رسالتك هنا..."
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-kitchen-600 text-white py-3 rounded-lg font-bold text-sm hover:bg-kitchen-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الإرسال...
                </div>
              ) : (
                'إرسال الرسالة'
              )}
            </button>

            {submitStatus === 'success' && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-3 py-2 rounded-lg text-center text-sm">
                <i className="ri-check-line text-lg ml-2"></i>
                تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.
              </div>
            )}

            {submitStatus === 'error' && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded-lg text-center text-sm">
                <i className="ri-error-warning-line text-lg ml-2"></i>
                حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.
              </div>
            )}
          </form>
        </div>
      </div>

      {/* Social Media */}
      <div className="px-4 mb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <h2 className="text-base font-bold text-gray-900 mb-3 text-center">تابعنا على</h2>
          <div className="flex justify-center space-x-4">
            <a
              href={getSocialMediaLink('snapchat')}
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center text-white hover:bg-yellow-600 transition-colors duration-300"
            >
              <i className="ri-snapchat-line text-xl"></i>
            </a>
            <a
              href={getSocialMediaLink('instagram')}
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
            >
              <i className="ri-instagram-line text-xl"></i>
            </a>
            <a
              href={getSocialMediaLink('tiktok')}
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-black rounded-lg flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300"
            >
              <i className="ri-tiktok-line text-xl"></i>
            </a>
            <a
              href={getSocialMediaLink('twitter')}
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-black rounded-lg flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300"
            >
              <i className="ri-twitter-x-line text-xl"></i>
            </a>
          </div>
        </div>
      </div>

      {/* Location */}
      <div className="px-4 pb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <h2 className="text-base font-bold text-gray-900 mb-3 text-center">موقعنا</h2>
          <div className="text-center text-gray-600 mb-3">
            <i className="ri-map-pin-line text-xl text-kitchen-600 mb-2"></i>
            <p className="text-sm">المملكة العربية السعودية</p>
            <p className="text-sm">الرياض</p>
          </div>
          <div className="bg-gray-100 rounded-lg h-32 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <i className="ri-map-line text-2xl mb-1"></i>
              <p className="text-sm">خريطة الموقع</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
