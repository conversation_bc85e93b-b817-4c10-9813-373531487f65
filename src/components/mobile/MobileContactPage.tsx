'use client'

import { useState } from 'react'
import { useSocialMedia } from '@/hooks/useSocialMedia'

export default function MobileContactPage() {
  const { getWhatsAppLink, getPhoneNumber, socialMedia } = useSocialMedia()
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    service: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const services = [
    'تصميم مطابخ عصرية',
    'تصميم مطابخ كلاسيكية',
    'تصميم خزانات ملابس',
    'تصميم خزانات مطبخ',
    'استشارة تصميم',
    'صيانة وتجديد',
    'أخرى'
  ]

  const contactMethods = [
    {
      icon: 'ri-whatsapp-line',
      title: 'واتساب',
      value: getPhoneNumber(),
      action: () => window.open(getWhatsAppLink(), '_blank'),
      color: 'from-green-500 to-green-600'
    },
    {
      icon: 'ri-phone-line',
      title: 'اتصال مباشر',
      value: getPhoneNumber(),
      action: () => window.open(`tel:${getPhoneNumber()}`, '_self'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: 'ri-mail-line',
      title: 'البريد الإلكتروني',
      value: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>', '_self'),
      color: 'from-purple-500 to-purple-600'
    }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        setSubmitStatus('success')
        setFormData({
          name: '',
          phone: '',
          email: '',
          service: '',
          message: ''
        })
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 pt-12 pb-8 px-6">
        <div className="text-center text-white">
          <h1 className="text-3xl font-bold mb-2">تواصل معنا</h1>
          <p className="text-primary-100">نحن هنا لمساعدتك في تحقيق حلمك</p>
        </div>
      </div>

      {/* Quick Contact Methods */}
      <div className="px-6 -mt-4 mb-8">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4 text-center">طرق التواصل السريع</h2>
          <div className="space-y-3">
            {contactMethods.map((method, index) => (
              <button
                key={index}
                onClick={method.action}
                className={`w-full bg-gradient-to-r ${method.color} text-white p-4 rounded-xl flex items-center justify-between hover:shadow-lg transition-all duration-300`}
              >
                <div className="flex items-center">
                  <i className={`${method.icon} text-2xl ml-3`}></i>
                  <div className="text-right">
                    <div className="font-bold">{method.title}</div>
                    <div className="text-sm opacity-90">{method.value}</div>
                  </div>
                </div>
                <i className="ri-arrow-left-s-line text-xl"></i>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Contact Form */}
      <div className="px-6 mb-8">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-6 text-center">أرسل لنا رسالة</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-gray-700 font-medium mb-2">الاسم الكامل *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                placeholder="أدخل اسمك الكامل"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">رقم الهاتف *</label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                placeholder="05xxxxxxxx"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">البريد الإلكتروني</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">نوع الخدمة المطلوبة</label>
              <select
                name="service"
                value={formData.service}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
              >
                <option value="">اختر نوع الخدمة</option>
                {services.map((service, index) => (
                  <option key={index} value={service}>{service}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">الرسالة *</label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                required
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 resize-none"
                placeholder="اكتب رسالتك هنا..."
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-primary-600 text-white py-4 rounded-xl font-bold text-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                  جاري الإرسال...
                </div>
              ) : (
                'إرسال الرسالة'
              )}
            </button>

            {submitStatus === 'success' && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-xl text-center">
                <i className="ri-check-line text-xl ml-2"></i>
                تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.
              </div>
            )}

            {submitStatus === 'error' && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl text-center">
                <i className="ri-error-warning-line text-xl ml-2"></i>
                حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.
              </div>
            )}
          </form>
        </div>
      </div>

      {/* Social Media */}
      <div className="px-6 mb-8">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4 text-center">تابعنا على</h2>
          <div className="flex justify-center space-x-4">
            {socialMedia.snapchat && (
              <a
                href={socialMedia.snapchat}
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center text-white hover:bg-yellow-600 transition-colors duration-300"
              >
                <i className="ri-snapchat-line text-xl"></i>
              </a>
            )}
            {socialMedia.instagram && (
              <a
                href={socialMedia.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
              >
                <i className="ri-instagram-line text-xl"></i>
              </a>
            )}
            {socialMedia.tiktok && (
              <a
                href={socialMedia.tiktok}
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 bg-black rounded-xl flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300"
              >
                <i className="ri-tiktok-line text-xl"></i>
              </a>
            )}
            {socialMedia.twitter && (
              <a
                href={socialMedia.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 bg-black rounded-xl flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300"
              >
                <i className="ri-twitter-x-line text-xl"></i>
              </a>
            )}
          </div>
        </div>
      </div>

      {/* Location */}
      <div className="px-6 pb-8">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4 text-center">موقعنا</h2>
          <div className="text-center text-gray-600 mb-4">
            <i className="ri-map-pin-line text-2xl text-primary-600 mb-2"></i>
            <p>المملكة العربية السعودية</p>
            <p>الرياض</p>
          </div>
          <div className="bg-gray-100 rounded-xl h-48 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <i className="ri-map-line text-4xl mb-2"></i>
              <p>خريطة الموقع</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
