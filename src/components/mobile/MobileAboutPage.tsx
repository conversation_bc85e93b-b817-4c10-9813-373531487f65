'use client'

import { useSocialMedia } from '@/hooks/useSocialMedia'
import Link from 'next/link'

export default function MobileAboutPage() {
  const { getWhatsAppLink, getPhoneNumber } = useSocialMedia()

  const values = [
    {
      icon: 'ri-award-line',
      title: 'الجودة',
      description: 'نستخدم أفضل المواد والخامات لضمان جودة عالية ومتانة طويلة الأمد',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: 'ri-customer-service-line',
      title: 'خدمة العملاء',
      description: 'نضع رضا العملاء في المقدمة ونسعى لتجاوز توقعاتهم',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: 'ri-lightbulb-line',
      title: 'الإبداع',
      description: 'نقدم تصاميم مبتكرة وحلول عملية تناسب احتياجات كل عميل',
      color: 'from-orange-500 to-orange-600'
    },
    {
      icon: 'ri-shield-check-line',
      title: 'الثقة',
      description: 'نبني علاقات طويلة الأمد مع عملائنا قائمة على الثقة والشفافية',
      color: 'from-purple-500 to-purple-600'
    }
  ]

  const stats = [
    { number: '15+', label: 'سنة خبرة', icon: 'ri-time-line' },
    { number: '500+', label: 'مشروع منجز', icon: 'ri-building-line' },
    { number: '100%', label: 'رضا العملاء', icon: 'ri-heart-line' },
    { number: '24/7', label: 'دعم فني', icon: 'ri-customer-service-line' }
  ]

  const team = [
    {
      icon: 'ri-pencil-ruler-line',
      title: 'فريق التصميم',
      description: 'مصممون محترفون متخصصون في إبداع تصاميم عصرية وكلاسيكية',
      count: '10+'
    },
    {
      icon: 'ri-hammer-line',
      title: 'فريق التنفيذ',
      description: 'حرفيون مهرة يتمتعون بخبرة واسعة في تنفيذ المشاريع',
      count: '25+'
    },
    {
      icon: 'ri-customer-service-2-line',
      title: 'فريق خدمة العملاء',
      description: 'متخصصون في خدمة العملاء يضمنون تجربة مميزة',
      count: '5+'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-kitchen-600 to-kitchen-800 pt-8 pb-12 px-4">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-3">من نحن</h1>
          <p className="text-kitchen-100 leading-relaxed text-sm">
            شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية
          </p>
        </div>
      </div>

      {/* Stats Section */}
      <div className="px-4 -mt-6 mb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <div className="grid grid-cols-2 gap-3">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-10 h-10 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <i className={`${stat.icon} text-white text-lg`}></i>
                </div>
                <div className="text-xl font-bold text-kitchen-600 mb-1">{stat.number}</div>
                <div className="text-gray-600 text-xs">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Story Section */}
      <div className="px-4 mb-6">
        <div className="bg-white rounded-xl shadow-lg p-4">
          <h2 className="text-lg font-bold text-gray-900 mb-3 text-center">قصتنا</h2>
          <div className="space-y-3 text-gray-700 leading-relaxed text-sm">
            <p>
              بدأت رحلة عجائب الخبراء منذ أكثر من 15 عاماً برؤية واضحة: تحويل المطابخ والخزائن من مجرد مساحات وظيفية إلى تحف فنية تجمع بين الجمال والعملية.
            </p>
            <p>
              نحن نؤمن بأن المطبخ هو قلب المنزل، والخزائن هي روح التنظيم. لذلك نسعى دائماً لتقديم حلول مبتكرة تلبي احتياجات عملائنا وتفوق توقعاتهم.
            </p>
          </div>
        </div>
      </div>

      {/* Vision & Mission */}
      <div className="px-4 mb-6">
        <div className="space-y-3">
          {/* Vision */}
          <div className="bg-white rounded-xl shadow-lg p-4">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-lg flex items-center justify-center ml-3">
                <i className="ri-eye-line text-white text-lg"></i>
              </div>
              <h3 className="text-lg font-bold text-gray-900">رؤيتنا</h3>
            </div>
            <p className="text-gray-700 leading-relaxed text-sm">
              أن نكون الشركة الرائدة في تصميم وتنفيذ المطابخ والخزائن في المملكة العربية السعودية، ونساهم في تحسين جودة الحياة من خلال إبداعاتنا.
            </p>
          </div>

          {/* Mission */}
          <div className="bg-white rounded-xl shadow-lg p-4">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-warm-500 to-warm-600 rounded-lg flex items-center justify-center ml-3">
                <i className="ri-target-line text-white text-lg"></i>
              </div>
              <h3 className="text-lg font-bold text-gray-900">رسالتنا</h3>
            </div>
            <p className="text-gray-700 leading-relaxed text-sm">
              نلتزم بتقديم خدمات متميزة في تصميم وتنفيذ المطابخ والخزائن باستخدام أحدث التقنيات وأفضل المواد، مع ضمان رضا العملاء والالتزام بالمواعيد.
            </p>
          </div>
        </div>
      </div>

      {/* Values */}
      <div className="px-4 mb-6">
        <h2 className="text-lg font-bold text-gray-900 text-center mb-4">قيمنا</h2>
        <div className="grid grid-cols-1 gap-3">
          {values.map((value, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-4">
              <div className="flex items-start">
                <div className={`w-10 h-10 bg-gradient-to-br ${value.color} rounded-lg flex items-center justify-center ml-3 flex-shrink-0`}>
                  <i className={`${value.icon} text-white text-lg`}></i>
                </div>
                <div>
                  <h3 className="text-base font-bold text-gray-900 mb-1">{value.title}</h3>
                  <p className="text-gray-600 text-xs leading-relaxed">{value.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Team */}
      <div className="px-4 mb-6">
        <h2 className="text-lg font-bold text-gray-900 text-center mb-4">فريق العمل</h2>
        <div className="space-y-3">
          {team.map((member, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-lg flex items-center justify-center ml-3">
                    <i className={`${member.icon} text-white text-lg`}></i>
                  </div>
                  <div>
                    <h3 className="text-base font-bold text-gray-900">{member.title}</h3>
                    <div className="text-kitchen-600 font-bold text-sm">{member.count}</div>
                  </div>
                </div>
              </div>
              <p className="text-gray-600 text-xs leading-relaxed">{member.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="px-4 pb-6">
        <div className="bg-gradient-to-r from-kitchen-600 to-kitchen-800 rounded-xl p-4 text-center text-white">
          <h2 className="text-lg font-bold mb-2">جاهز لبدء مشروعك معنا؟</h2>
          <p className="text-kitchen-100 mb-4 text-xs">
            تواصل معنا اليوم واحصل على استشارة مجانية وعرض سعر مخصص لمشروعك
          </p>

          <div className="space-y-2">
            <Link
              href={getWhatsAppLink()}
              target="_blank"
              rel="noopener noreferrer"
              className="block bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg font-medium transition-colors duration-300 text-sm"
            >
              <i className="ri-whatsapp-line text-lg ml-2"></i>
              تواصل عبر واتساب
            </Link>

            <Link
              href={`tel:${getPhoneNumber()}`}
              className="block bg-white text-kitchen-600 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-300 text-sm"
            >
              <i className="ri-phone-line text-lg ml-2"></i>
              اتصل بنا الآن
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
