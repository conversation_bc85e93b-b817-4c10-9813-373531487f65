'use client'

import { useState, useEffect } from 'react'

export default function MobileToggle() {
  const [isMobile, setIsMobile] = useState(false)
  const [showToggle, setShowToggle] = useState(false)

  useEffect(() => {
    const checkDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
      const isSmallScreen = window.innerWidth <= 768
      
      setIsMobile(isMobileDevice || isSmallScreen)
      
      // Show toggle only on desktop when screen is small or on mobile devices
      setShowToggle(isSmallScreen || isMobileDevice)
    }

    checkDevice()
    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  const toggleView = () => {
    const currentMode = localStorage.getItem('viewMode') || 'auto'
    const newMode = currentMode === 'mobile' ? 'desktop' : 'mobile'
    localStorage.setItem('viewMode', newMode)
    window.location.reload()
  }

  if (!showToggle) return null

  return (
    <button
      onClick={toggleView}
      className="fixed bottom-24 left-4 z-50 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 md:bottom-4"
      title={isMobile ? 'عرض نسخة سطح المكتب' : 'عرض النسخة المحمولة'}
    >
      <i className={`${isMobile ? 'ri-computer-line' : 'ri-smartphone-line'} text-xl`}></i>
    </button>
  )
}
