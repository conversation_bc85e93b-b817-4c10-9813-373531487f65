'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'

interface MobileLayoutProps {
  children: React.ReactNode
}

export default function MobileLayout({ children }: MobileLayoutProps) {
  const pathname = usePathname()
  // Remove scroll hiding functionality - keep navbar always visible
  const isVisible = true

  const navItems = [
    {
      href: '/',
      icon: 'ri-home-5-line',
      activeIcon: 'ri-home-5-fill',
      label: 'الرئيسية'
    },
    {
      href: '/kitchens',
      icon: 'ri-restaurant-line',
      activeIcon: 'ri-restaurant-fill',
      label: 'المطابخ'
    },
    {
      href: '/cabinets',
      icon: 'ri-archive-line',
      activeIcon: 'ri-archive-fill',
      label: 'الخزانات'
    },
    {
      href: '/about',
      icon: 'ri-information-line',
      activeIcon: 'ri-information-fill',
      label: 'من نحن'
    },
    {
      href: '/contact',
      icon: 'ri-phone-line',
      activeIcon: 'ri-phone-fill',
      label: 'تواصل معنا'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Main Content */}
      <main className="pb-20">
        {children}
      </main>

      {/* Bottom Navigation */}
      <nav
        className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 bottom-nav-fixed"
        style={{
          boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)'
        }}
      >
        <div className="flex items-center justify-around py-2 px-4 max-w-md mx-auto">
          {navItems.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex flex-col items-center justify-center py-2 px-3 rounded-xl transition-all duration-300 min-w-0 flex-1 ${
                  isActive
                    ? 'text-kitchen-600 bg-kitchen-50'
                    : 'text-gray-500 hover:text-kitchen-600 hover:bg-gray-50'
                }`}
              >
                <i 
                  className={`${isActive ? item.activeIcon : item.icon} text-xl mb-1 transition-all duration-300 ${
                    isActive ? 'scale-110' : ''
                  }`}
                ></i>
                <span className={`text-xs font-medium transition-all duration-300 ${
                  isActive ? 'text-kitchen-600' : 'text-gray-500'
                }`}>
                  {item.label}
                </span>
                {isActive && (
                  <div className="absolute -top-1 w-1 h-1 bg-kitchen-600 rounded-full"></div>
                )}
              </Link>
            )
          })}
        </div>
      </nav>
    </div>
  )
}
