'use client'

import { useState, useEffect } from 'react'
import { useSocialMedia } from '@/hooks/useSocialMedia'

interface Kitchen {
  id: number
  title: string
  description: string
  category_name: string
  images: Array<{
    id: number
    kitchen_id: number
    image_url: string
    sort_order: number
  }>
}

export default function MobileKitchensPage() {
  const { getWhatsAppLink } = useSocialMedia()
  const [kitchens, setKitchens] = useState<Kitchen[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedKitchen, setSelectedKitchen] = useState<Kitchen | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  useEffect(() => {
    fetchKitchens()
  }, [])

  const fetchKitchens = async () => {
    try {
      const response = await fetch('/api/kitchens')
      if (response.ok) {
        const data = await response.json()
        setKitchens(data)
      }
    } catch (error) {
      console.error('Error fetching kitchens:', error)
    } finally {
      setLoading(false)
    }
  }

  const openModal = (kitchen: Kitchen) => {
    setSelectedKitchen(kitchen)
    setCurrentImageIndex(0)
    document.body.style.overflow = 'hidden'
  }

  const closeModal = () => {
    setSelectedKitchen(null)
    setCurrentImageIndex(0)
    document.body.style.overflow = 'unset'
  }

  const nextImage = () => {
    if (selectedKitchen) {
      setCurrentImageIndex((prev) => 
        prev === selectedKitchen.images.length - 1 ? 0 : prev + 1
      )
    }
  }

  const prevImage = () => {
    if (selectedKitchen) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? selectedKitchen.images.length - 1 : prev - 1
      )
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المطابخ...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-kitchen-600 to-kitchen-800 pt-8 pb-6 px-4">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-2">المطابخ</h1>
          <p className="text-kitchen-100">تصاميم عصرية وكلاسيكية مميزة</p>
        </div>
      </div>

      {/* Kitchens Grid */}
      <div className="p-4">
        {kitchens.length === 0 ? (
          <div className="text-center py-8">
            <i className="ri-restaurant-line text-4xl text-gray-400 mb-3"></i>
            <p className="text-gray-600 text-base">لا توجد مطابخ متاحة حالياً</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {kitchens.map((kitchen) => {
              const firstImage = kitchen.images && kitchen.images.length > 0
                ? kitchen.images[0].image_url
                : 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'

              return (
                <div
                  key={kitchen.id}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
                  onClick={() => openModal(kitchen)}
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={firstImage}
                      alt={kitchen.title}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      onError={(e) => {
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
                      }}
                    />
                    <div className="absolute top-3 right-3 bg-kitchen-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {kitchen.category_name || 'مطبخ'}
                    </div>
                    {kitchen.images && kitchen.images.length > 1 && (
                      <div className="absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                        +{kitchen.images.length - 1} صور
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{kitchen.title}</h3>
                    <p className="text-gray-600 mb-3 line-clamp-2 text-sm">{kitchen.description}</p>
                    <button className="w-full bg-kitchen-600 text-white py-2 rounded-lg font-medium hover:bg-kitchen-700 transition-colors duration-300 text-sm">
                      عرض التفاصيل
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Modal */}
      {selectedKitchen && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-bold text-gray-900">{selectedKitchen.title}</h3>
              <button
                onClick={closeModal}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-300"
              >
                <i className="ri-close-line text-lg text-gray-600"></i>
              </button>
            </div>

            {/* Image Gallery */}
            <div className="relative">
              <div className="h-48 overflow-hidden">
                {selectedKitchen.images && selectedKitchen.images.length > 0 ? (
                  <img
                    src={selectedKitchen.images[currentImageIndex]?.image_url || 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'}
                    alt={selectedKitchen.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
                    }}
                  />
                ) : (
                  <img
                    src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop"
                    alt={selectedKitchen.title}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>

              {selectedKitchen.images && selectedKitchen.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                  >
                    <i className="ri-arrow-left-s-line text-lg"></i>
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                  >
                    <i className="ri-arrow-right-s-line text-lg"></i>
                  </button>

                  {/* Image Indicators */}
                  <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    {selectedKitchen.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                          index === currentImageIndex ? 'bg-white scale-125' : 'bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>

            {/* Modal Content */}
            <div className="p-4">
              <div className="mb-3">
                <span className="bg-kitchen-100 text-kitchen-800 px-2 py-1 rounded-full text-xs font-medium">
                  {selectedKitchen.category_name || 'مطبخ'}
                </span>
              </div>
              <p className="text-gray-700 mb-4 leading-relaxed text-sm">{selectedKitchen.description}</p>

              {/* Social Media Icons */}
              <div className="flex justify-center space-x-4 mb-4">
                <a
                  href={getWhatsAppLink()}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-colors duration-300"
                >
                  <i className="ri-whatsapp-line text-sm"></i>
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
                >
                  <i className="ri-instagram-line text-sm"></i>
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300"
                >
                  <i className="ri-twitter-x-line text-sm"></i>
                </a>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <button
                  onClick={() => window.open(getWhatsAppLink(), '_blank')}
                  className="w-full bg-green-500 text-white py-2 rounded-lg font-medium hover:bg-green-600 transition-colors duration-300 flex items-center justify-center gap-2 text-sm"
                >
                  <i className="ri-whatsapp-line text-lg"></i>
                  تواصل عبر واتساب
                </button>
                <button
                  onClick={closeModal}
                  className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-300 text-sm"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
