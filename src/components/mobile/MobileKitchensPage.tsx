'use client'

import { useState, useEffect } from 'react'
import { useSocialMedia } from '@/hooks/useSocialMedia'

interface Kitchen {
  id: number
  name: string
  category: string
  images: string[]
  description: string
}

export default function MobileKitchensPage() {
  const { getWhatsAppLink } = useSocialMedia()
  const [kitchens, setKitchens] = useState<Kitchen[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedKitchen, setSelectedKitchen] = useState<Kitchen | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  useEffect(() => {
    fetchKitchens()
  }, [])

  const fetchKitchens = async () => {
    try {
      const response = await fetch('/api/kitchens')
      if (response.ok) {
        const data = await response.json()
        setKitchens(data)
      }
    } catch (error) {
      console.error('Error fetching kitchens:', error)
    } finally {
      setLoading(false)
    }
  }

  const openModal = (kitchen: Kitchen) => {
    setSelectedKitchen(kitchen)
    setCurrentImageIndex(0)
    document.body.style.overflow = 'hidden'
  }

  const closeModal = () => {
    setSelectedKitchen(null)
    setCurrentImageIndex(0)
    document.body.style.overflow = 'unset'
  }

  const nextImage = () => {
    if (selectedKitchen) {
      setCurrentImageIndex((prev) => 
        prev === selectedKitchen.images.length - 1 ? 0 : prev + 1
      )
    }
  }

  const prevImage = () => {
    if (selectedKitchen) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? selectedKitchen.images.length - 1 : prev - 1
      )
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المطابخ...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 pt-12 pb-8 px-6">
        <div className="text-center text-white">
          <h1 className="text-3xl font-bold mb-2">المطابخ</h1>
          <p className="text-primary-100">تصاميم عصرية وكلاسيكية مميزة</p>
        </div>
      </div>

      {/* Kitchens Grid */}
      <div className="p-6">
        {kitchens.length === 0 ? (
          <div className="text-center py-12">
            <i className="ri-restaurant-line text-6xl text-gray-400 mb-4"></i>
            <p className="text-gray-600 text-lg">لا توجد مطابخ متاحة حالياً</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            {kitchens.map((kitchen) => (
              <div
                key={kitchen.id}
                className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
                onClick={() => openModal(kitchen)}
              >
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={kitchen.images[0] || '/api/placeholder/400/300'}
                    alt={kitchen.name}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <div className="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {kitchen.category}
                  </div>
                  {kitchen.images.length > 1 && (
                    <div className="absolute bottom-4 left-4 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                      +{kitchen.images.length - 1} صور
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{kitchen.name}</h3>
                  <p className="text-gray-600 mb-4 line-clamp-2">{kitchen.description}</p>
                  <button className="w-full bg-primary-600 text-white py-3 rounded-xl font-medium hover:bg-primary-700 transition-colors duration-300">
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      {selectedKitchen && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-xl font-bold text-gray-900">{selectedKitchen.name}</h3>
              <button
                onClick={closeModal}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-300"
              >
                <i className="ri-close-line text-xl text-gray-600"></i>
              </button>
            </div>

            {/* Image Gallery */}
            <div className="relative">
              <div className="h-64 overflow-hidden">
                <img
                  src={selectedKitchen.images[currentImageIndex] || '/api/placeholder/400/300'}
                  alt={selectedKitchen.name}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {selectedKitchen.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                  >
                    <i className="ri-arrow-left-s-line text-xl"></i>
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                  >
                    <i className="ri-arrow-right-s-line text-xl"></i>
                  </button>
                  
                  {/* Image Indicators */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {selectedKitchen.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                          index === currentImageIndex ? 'bg-white scale-125' : 'bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="mb-4">
                <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                  {selectedKitchen.category}
                </span>
              </div>
              <p className="text-gray-700 mb-6 leading-relaxed">{selectedKitchen.description}</p>
              
              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={() => window.open(getWhatsAppLink(), '_blank')}
                  className="w-full bg-green-500 text-white py-3 rounded-xl font-medium hover:bg-green-600 transition-colors duration-300 flex items-center justify-center gap-2"
                >
                  <i className="ri-whatsapp-line text-xl"></i>
                  تواصل عبر واتساب
                </button>
                <button
                  onClick={closeModal}
                  className="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors duration-300"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
