'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import MobileLayout from './MobileLayout'
import MobileHomePage from './MobileHomePage'
import MobileKitchensPage from './MobileKitchensPage'
import MobileCabinetsPage from './MobileCabinetsPage'
import MobileAboutPage from './MobileAboutPage'
import MobileContactPage from './MobileContactPage'

interface MobileDetectorProps {
  children: React.ReactNode
}

export default function MobileDetector({ children }: MobileDetectorProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const pathname = usePathname()

  useEffect(() => {
    const checkDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
      const isSmallScreen = window.innerWidth <= 768

      setIsMobile(isMobileDevice || isSmallScreen)
      setIsLoading(false)
    }

    // Check on mount
    checkDevice()

    // Check on resize
    const handleResize = () => {
      const isSmallScreen = window.innerWidth <= 768
      const userAgent = navigator.userAgent.toLowerCase()
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)

      setIsMobile(isMobileDevice || isSmallScreen)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Show loading spinner while detecting
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  // If not mobile, show desktop version
  if (!isMobile) {
    return <>{children}</>
  }

  // Mobile version - render appropriate page component
  const renderMobilePage = () => {
    switch (pathname) {
      case '/':
        return <MobileHomePage />
      case '/kitchens':
        return <MobileKitchensPage />
      case '/cabinets':
        return <MobileCabinetsPage />
      case '/about':
        return <MobileAboutPage />
      case '/contact':
        return <MobileContactPage />
      default:
        // For admin pages or other routes, show desktop version
        if (pathname.startsWith('/admin')) {
          return <>{children}</>
        }
        // For unknown routes, show mobile home page
        return <MobileHomePage />
    }
  }

  // If it's an admin route, don't use mobile layout
  if (pathname.startsWith('/admin')) {
    return <>{children}</>
  }

  return (
    <MobileLayout>
      {renderMobilePage()}
    </MobileLayout>
  )
}
