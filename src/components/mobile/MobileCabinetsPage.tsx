'use client'

import { useState, useEffect } from 'react'
import { useSocialMedia } from '@/hooks/useSocialMedia'

interface Cabinet {
  id: number
  title: string
  description: string
  category_name: string
  images: Array<{
    id: number
    cabinet_id: number
    image_url: string
    sort_order: number
  }>
}

export default function MobileCabinetsPage() {
  const { getWhatsAppLink } = useSocialMedia()
  const [cabinets, setCabinets] = useState<Cabinet[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCabinet, setSelectedCabinet] = useState<Cabinet | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  useEffect(() => {
    fetchCabinets()
  }, [])

  const fetchCabinets = async () => {
    try {
      const response = await fetch('/api/cabinets')
      if (response.ok) {
        const data = await response.json()
        setCabinets(data)
      }
    } catch (error) {
      console.error('Error fetching cabinets:', error)
    } finally {
      setLoading(false)
    }
  }

  const openModal = (cabinet: Cabinet) => {
    setSelectedCabinet(cabinet)
    setCurrentImageIndex(0)
    document.body.style.overflow = 'hidden'
  }

  const closeModal = () => {
    setSelectedCabinet(null)
    setCurrentImageIndex(0)
    document.body.style.overflow = 'unset'
  }

  const nextImage = () => {
    if (selectedCabinet) {
      setCurrentImageIndex((prev) => 
        prev === selectedCabinet.images.length - 1 ? 0 : prev + 1
      )
    }
  }

  const prevImage = () => {
    if (selectedCabinet) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? selectedCabinet.images.length - 1 : prev - 1
      )
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الخزانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-accent-600 to-accent-800 pt-8 pb-6 px-4">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-2">الخزانات</h1>
          <p className="text-accent-100">حلول تخزين ذكية ومميزة</p>
        </div>
      </div>

      {/* Cabinets Grid */}
      <div className="p-4">
        {cabinets.length === 0 ? (
          <div className="text-center py-8">
            <i className="ri-archive-line text-4xl text-gray-400 mb-3"></i>
            <p className="text-gray-600 text-base">لا توجد خزانات متاحة حالياً</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {cabinets.map((cabinet) => {
              const firstImage = cabinet.images && cabinet.images.length > 0
                ? cabinet.images[0].image_url
                : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'

              return (
                <div
                  key={cabinet.id}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
                  onClick={() => openModal(cabinet)}
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={firstImage}
                      alt={cabinet.title}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      onError={(e) => {
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'
                      }}
                    />
                    <div className="absolute top-3 right-3 bg-accent-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {cabinet.category_name || 'خزانة'}
                    </div>
                    {cabinet.images && cabinet.images.length > 1 && (
                      <div className="absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs">
                        +{cabinet.images.length - 1} صور
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{cabinet.title}</h3>
                    <p className="text-gray-600 mb-3 line-clamp-2 text-sm">{cabinet.description}</p>
                    <button className="w-full bg-accent-600 text-white py-2 rounded-lg font-medium hover:bg-accent-700 transition-colors duration-300 text-sm">
                      عرض التفاصيل
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Modal */}
      {selectedCabinet && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-bold text-gray-900">{selectedCabinet.title}</h3>
              <button
                onClick={closeModal}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-300"
              >
                <i className="ri-close-line text-lg text-gray-600"></i>
              </button>
            </div>

            {/* Image Gallery */}
            <div className="relative">
              <div className="h-48 overflow-hidden">
                {selectedCabinet.images && selectedCabinet.images.length > 0 ? (
                  <img
                    src={selectedCabinet.images[currentImageIndex]?.image_url || 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'}
                    alt={selectedCabinet.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'
                    }}
                  />
                ) : (
                  <img
                    src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop"
                    alt={selectedCabinet.title}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>

              {selectedCabinet.images && selectedCabinet.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                  >
                    <i className="ri-arrow-left-s-line text-lg"></i>
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                  >
                    <i className="ri-arrow-right-s-line text-lg"></i>
                  </button>

                  {/* Image Indicators */}
                  <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    {selectedCabinet.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                          index === currentImageIndex ? 'bg-white scale-125' : 'bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>

            {/* Modal Content */}
            <div className="p-4">
              <div className="mb-3">
                <span className="bg-accent-100 text-accent-800 px-2 py-1 rounded-full text-xs font-medium">
                  {selectedCabinet.category_name || 'خزانة'}
                </span>
              </div>
              <p className="text-gray-700 mb-4 leading-relaxed text-sm">{selectedCabinet.description}</p>

              {/* Social Media Icons */}
              <div className="flex justify-center space-x-4 mb-4">
                <a
                  href={getWhatsAppLink()}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-colors duration-300"
                >
                  <i className="ri-whatsapp-line text-sm"></i>
                </a>
                <a
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
                >
                  <i className="ri-instagram-line text-sm"></i>
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-8 h-8 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300"
                >
                  <i className="ri-twitter-x-line text-sm"></i>
                </a>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <button
                  onClick={() => window.open(getWhatsAppLink(), '_blank')}
                  className="w-full bg-green-500 text-white py-2 rounded-lg font-medium hover:bg-green-600 transition-colors duration-300 flex items-center justify-center gap-2 text-sm"
                >
                  <i className="ri-whatsapp-line text-lg"></i>
                  تواصل عبر واتساب
                </button>
                <button
                  onClick={closeModal}
                  className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-300 text-sm"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
