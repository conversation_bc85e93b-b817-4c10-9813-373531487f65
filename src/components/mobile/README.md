# مكونات الهاتف المحمول - Mobile Components

## نظرة عامة

هذا المجلد يحتوي على مكونات مخصصة لتجربة الهاتف المحمول التي تحاكي تجربة التطبيقات الأصلية مع ناف بار سفلي وتصميم مميز.

## المكونات المتاحة

### 1. MobileDetector
- **الوصف**: مكون رئيسي يكتشف نوع الجهاز ويعرض النسخة المناسبة
- **الميزات**:
  - كشف تلقائي للأجهزة المحمولة
  - تبديل تلقائي بين النسخة المحمولة والمكتبية

### 2. MobileLayout
- **الوصف**: تخطيط أساسي للصفحات المحمولة مع ناف بار سفلي
- **الميزات**:
  - ناف بار سفلي ثابت مع 5 أقسام رئيسية
  - إخفاء/إظهار الناف بار عند التمرير
  - تأثيرات بصرية متقدمة (blur, shadow)
  - أيقونات تفاعلية مع حالات نشطة

### 3. MobileHomePage
- **الوصف**: الصفحة الرئيسية المحمولة
- **الميزات**:
  - عرض شرائح تلقائي للبانر الرئيسي
  - أزرار إجراءات سريعة (واتساب، اتصال)
  - عرض الميزات في شبكة
  - معاينة المنتجات مع روابط للصفحات المخصصة

### 4. MobileKitchensPage
- **الوصف**: صفحة المطابخ المحمولة
- **الميزات**:
  - عرض المطابخ في بطاقات عمودية
  - مودال تفاعلي لعرض التفاصيل
  - معرض صور مع مؤشرات
  - أزرار تواصل مباشرة

### 5. MobileCabinetsPage
- **الوصف**: صفحة الخزانات المحمولة
- **الميزات**:
  - تصميم مشابه لصفحة المطابخ
  - ألوان مميزة للخزانات (أخضر)
  - نفس الوظائف التفاعلية

### 6. MobileAboutPage
- **الوصف**: صفحة "من نحن" المحمولة
- **الميزات**:
  - إحصائيات الشركة في بطاقات
  - قصة الشركة والرؤية والرسالة
  - القيم في بطاقات منفصلة
  - معلومات فريق العمل
  - دعوة للعمل في النهاية

### 7. MobileContactPage
- **الوصف**: صفحة التواصل المحمولة
- **الميزات**:
  - طرق تواصل سريعة (واتساب، هاتف، إيميل)
  - نموذج تواصل تفاعلي
  - روابط وسائل التواصل الاجتماعي
  - خريطة الموقع



## كيفية الاستخدام

### التثبيت التلقائي
المكونات مثبتة تلقائياً في `layout.tsx` عبر `MobileDetector`.

### الكشف التلقائي
النظام يكتشف نوع الجهاز تلقائياً ويعرض النسخة المناسبة:
- **الأجهزة المحمولة**: عرض النسخة المحمولة
- **الشاشات الصغيرة** (أقل من 768px): عرض النسخة المحمولة
- **أجهزة سطح المكتب**: عرض النسخة المكتبية

## الأنماط المخصصة

### الرسوم المتحركة
- `animate-fade-in`: ظهور تدريجي
- `animate-fade-in-delay`: ظهور تدريجي مع تأخير
- `animate-fade-in-delay-2`: ظهور تدريجي مع تأخير أطول

### أدوات مساعدة
- `line-clamp-2`: قطع النص بعد سطرين
- `line-clamp-3`: قطع النص بعد ثلاثة أسطر
- `safe-area-bottom`: مساحة آمنة في الأسفل للأجهزة الحديثة

## التخصيص

### تغيير الألوان
يمكن تخصيص الألوان عبر متغيرات Tailwind في `tailwind.config.js`:

```javascript
colors: {
  primary: {
    50: '#eff6ff',
    500: '#3b82f6',
    600: '#2563eb',
    // ...
  }
}
```

### إضافة صفحات جديدة
1. أنشئ مكون جديد في هذا المجلد
2. أضفه إلى `MobileDetector.tsx` في دالة `renderMobilePage`
3. أضف الرابط إلى `navItems` في `MobileLayout.tsx`

## الاعتبارات التقنية

### الأداء
- استخدام `useEffect` لكشف الجهاز مرة واحدة
- تحميل المكونات حسب الحاجة
- تحسين الصور والرسوم المتحركة

### إمكانية الوصول
- دعم قارئات الشاشة
- تباين ألوان مناسب
- أحجام أزرار مناسبة للمس

### التوافق
- دعم جميع المتصفحات الحديثة
- تجربة متسقة عبر أنظمة التشغيل المختلفة
- دعم الأجهزة اللوحية والهواتف

## الصيانة

### إضافة ميزات جديدة
1. تأكد من التوافق مع النسخة المكتبية
2. اختبر على أجهزة مختلفة
3. حدث الوثائق

### إصلاح المشاكل
1. تحقق من console للأخطاء
2. اختبر كشف الجهاز
3. تأكد من تحميل الأنماط بشكل صحيح
