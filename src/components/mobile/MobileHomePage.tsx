'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSocialMedia } from '@/hooks/useSocialMedia'

export default function MobileHomePage() {
  const { getWhatsAppLink, getPhoneNumber } = useSocialMedia()
  const [currentSlide, setCurrentSlide] = useState(0)

  const heroSlides = [
    {
      title: 'مطابخ عصرية',
      subtitle: 'تصاميم حديثة تناسب عصرنا',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      gradient: 'from-kitchen-600 to-kitchen-800'
    },
    {
      title: 'مطابخ كلاسيكية',
      subtitle: 'أناقة تقليدية بلمسة عصرية',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      gradient: 'from-warm-600 to-warm-800'
    },
    {
      title: 'خزانات فاخرة',
      subtitle: 'حلول تخزين ذكية ومميزة',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
      gradient: 'from-accent-600 to-accent-800'
    }
  ]

  const features = [
    {
      icon: 'ri-award-line',
      title: 'جودة عالية',
      description: 'أفضل المواد والخامات'
    },
    {
      icon: 'ri-time-line',
      title: 'تسليم سريع',
      description: 'التزام بالمواعيد المحددة'
    },
    {
      icon: 'ri-customer-service-line',
      title: 'خدمة مميزة',
      description: 'دعم على مدار الساعة'
    },
    {
      icon: 'ri-shield-check-line',
      title: 'ضمان شامل',
      description: 'ضمان على جميع المنتجات'
    }
  ]

  const quickActions = [
    {
      title: 'استشارة مجانية',
      icon: 'ri-chat-3-line',
      color: 'from-kitchen-500 to-kitchen-600',
      action: () => window.open(getWhatsAppLink(), '_blank')
    },
    {
      title: 'اتصل بنا',
      icon: 'ri-phone-line',
      color: 'from-warm-500 to-warm-600',
      action: () => window.open(`tel:${getPhoneNumber()}`, '_self')
    }
  ]

  // Auto slide
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 4000)
    return () => clearInterval(timer)
  }, [heroSlides.length])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-[70vh] overflow-hidden">
        {heroSlides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
              index === currentSlide ? 'translate-x-0' : 'translate-x-full'
            }`}
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${slide.gradient} opacity-90`}></div>
            <div 
              className="absolute inset-0 bg-cover bg-center"
              style={{ backgroundImage: `url(${slide.image})` }}
            ></div>
            <div className="relative z-10 flex flex-col justify-center items-center h-full text-center px-4">
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-3 animate-fade-in">
                {slide.title}
              </h1>
              <p className="text-lg text-white/90 mb-6 animate-fade-in-delay">
                {slide.subtitle}
              </p>
              <Link
                href="/contact"
                className="bg-white text-gray-900 px-6 py-3 rounded-full font-bold text-base hover:bg-gray-100 transition-all duration-300 shadow-xl transform hover:scale-105 animate-fade-in-delay-2"
              >
                احصل على عرض سعر
              </Link>
            </div>
          </div>
        ))}
        
        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-6 px-4 bg-white">
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`bg-gradient-to-r ${action.color} text-white p-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105`}
            >
              <i className={`${action.icon} text-2xl mb-2 block`}></i>
              <span className="font-bold text-sm">{action.title}</span>
            </button>
          ))}
        </div>
      </section>

      {/* Features */}
      <section className="py-8 px-4">
        <h2 className="text-xl font-bold text-gray-900 text-center mb-6">
          لماذا تختار عجائب الخبراء؟
        </h2>
        <div className="grid grid-cols-2 gap-4">
          {features.map((feature, index) => (
            <div key={index} className="bg-white p-4 rounded-xl shadow-lg text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className={`${feature.icon} text-white text-lg`}></i>
              </div>
              <h3 className="font-bold text-gray-900 mb-2 text-sm">{feature.title}</h3>
              <p className="text-gray-600 text-xs">{feature.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Products Preview */}
      <section className="py-8 px-4 bg-white">
        <h2 className="text-xl font-bold text-gray-900 text-center mb-6">
          منتجاتنا
        </h2>
        <div className="grid grid-cols-1 gap-4">
          <Link href="/kitchens" className="group">
            <div className="relative overflow-hidden rounded-xl shadow-lg">
              <div className="h-32 bg-gradient-to-br from-kitchen-500 to-kitchen-600"></div>
              <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-300"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <i className="ri-restaurant-line text-2xl mb-1"></i>
                  <h3 className="text-lg font-bold">المطابخ</h3>
                  <p className="text-white/90 text-sm">تصاميم عصرية وكلاسيكية</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/cabinets" className="group">
            <div className="relative overflow-hidden rounded-xl shadow-lg">
              <div className="h-32 bg-gradient-to-br from-accent-500 to-accent-600"></div>
              <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-300"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <i className="ri-archive-line text-2xl mb-1"></i>
                  <h3 className="text-lg font-bold">الخزانات</h3>
                  <p className="text-white/90 text-sm">حلول تخزين ذكية</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </section>
    </div>
  )
}
