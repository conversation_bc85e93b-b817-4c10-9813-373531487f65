'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useSocialMedia } from '@/hooks/useSocialMedia'

export default function MobileHomePage() {
  const { getWhatsAppLink, getPhoneNumber } = useSocialMedia()
  const [currentSlide, setCurrentSlide] = useState(0)

  const heroSlides = [
    {
      title: 'مطابخ عصرية',
      subtitle: 'تصاميم حديثة تناسب عصرنا',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      gradient: 'from-blue-600 to-purple-600'
    },
    {
      title: 'مطابخ كلاسيكية',
      subtitle: 'أناقة تقليدية بلمسة عصرية',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      gradient: 'from-amber-600 to-orange-600'
    },
    {
      title: 'خزانات فاخرة',
      subtitle: 'حلول تخزين ذكية ومميزة',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
      gradient: 'from-emerald-600 to-teal-600'
    }
  ]

  const features = [
    {
      icon: 'ri-award-line',
      title: 'جودة عالية',
      description: 'أفضل المواد والخامات'
    },
    {
      icon: 'ri-time-line',
      title: 'تسليم سريع',
      description: 'التزام بالمواعيد المحددة'
    },
    {
      icon: 'ri-customer-service-line',
      title: 'خدمة مميزة',
      description: 'دعم على مدار الساعة'
    },
    {
      icon: 'ri-shield-check-line',
      title: 'ضمان شامل',
      description: 'ضمان على جميع المنتجات'
    }
  ]

  const quickActions = [
    {
      title: 'استشارة مجانية',
      icon: 'ri-chat-3-line',
      color: 'from-blue-500 to-blue-600',
      action: () => window.open(getWhatsAppLink(), '_blank')
    },
    {
      title: 'اتصل بنا',
      icon: 'ri-phone-line',
      color: 'from-green-500 to-green-600',
      action: () => window.open(`tel:${getPhoneNumber()}`, '_self')
    }
  ]

  // Auto slide
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 4000)
    return () => clearInterval(timer)
  }, [heroSlides.length])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-screen overflow-hidden">
        {heroSlides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
              index === currentSlide ? 'translate-x-0' : 'translate-x-full'
            }`}
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${slide.gradient} opacity-90`}></div>
            <div 
              className="absolute inset-0 bg-cover bg-center"
              style={{ backgroundImage: `url(${slide.image})` }}
            ></div>
            <div className="relative z-10 flex flex-col justify-center items-center h-full text-center px-6">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 animate-fade-in">
                {slide.title}
              </h1>
              <p className="text-xl text-white/90 mb-8 animate-fade-in-delay">
                {slide.subtitle}
              </p>
              <Link
                href="/contact"
                className="bg-white text-gray-900 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-xl transform hover:scale-105 animate-fade-in-delay-2"
              >
                احصل على عرض سعر
              </Link>
            </div>
          </div>
        ))}
        
        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-8 px-6 bg-white">
        <div className="grid grid-cols-2 gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`bg-gradient-to-r ${action.color} text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105`}
            >
              <i className={`${action.icon} text-3xl mb-3 block`}></i>
              <span className="font-bold text-lg">{action.title}</span>
            </button>
          ))}
        </div>
      </section>

      {/* Features */}
      <section className="py-12 px-6">
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
          لماذا تختار عجائب الخبراء؟
        </h2>
        <div className="grid grid-cols-2 gap-6">
          {features.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-2xl shadow-lg text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <i className={`${feature.icon} text-white text-2xl`}></i>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Products Preview */}
      <section className="py-12 px-6 bg-white">
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
          منتجاتنا
        </h2>
        <div className="grid grid-cols-1 gap-6">
          <Link href="/kitchens" className="group">
            <div className="relative overflow-hidden rounded-2xl shadow-lg">
              <div className="h-48 bg-gradient-to-br from-blue-500 to-purple-600"></div>
              <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-300"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <i className="ri-restaurant-line text-4xl mb-2"></i>
                  <h3 className="text-xl font-bold">المطابخ</h3>
                  <p className="text-white/90">تصاميم عصرية وكلاسيكية</p>
                </div>
              </div>
            </div>
          </Link>
          
          <Link href="/cabinets" className="group">
            <div className="relative overflow-hidden rounded-2xl shadow-lg">
              <div className="h-48 bg-gradient-to-br from-emerald-500 to-teal-600"></div>
              <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-300"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <i className="ri-archive-line text-4xl mb-2"></i>
                  <h3 className="text-xl font-bold">الخزانات</h3>
                  <p className="text-white/90">حلول تخزين ذكية</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </section>
    </div>
  )
}
