'use client'

import { motion } from 'framer-motion'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

interface Testimonial {
  id: number
  name: string
  location: string
  rating: number
  comment: string
  avatar?: string
}

const Testimonials = () => {
  // Default testimonials data
  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: 'أحمد محمد',
      location: 'الرياض',
      rating: 5,
      comment: 'خدمة ممتازة وجودة عالية في التنفيذ. فريق محترف ومتعاون. أنصح بالتعامل معهم بشدة.',
    },
    {
      id: 2,
      name: 'فاطمة العلي',
      location: 'جدة',
      rating: 5,
      comment: 'تصميم رائع ومطبخ أحلامي أصبح حقيقة. شكراً لفريق عجائب الخبراء على الإبداع والاحترافية.',
    },
    {
      id: 3,
      name: 'خا<PERSON>د السعد',
      location: 'الدمام',
      rating: 5,
      comment: 'سرعة في التنفيذ والتزام بالمواعيد. المطبخ تم تسليمه في الوقت المحدد وبجودة فائقة.',
    },
    {
      id: 4,
      name: 'نورا أحمد',
      location: 'المدينة المنورة',
      rating: 5,
      comment: 'تعامل راقي وخدمة عملاء ممتازة. الخزانات جميلة جداً وتناسب ديكور المنزل بشكل مثالي.',
    },
    {
      id: 5,
      name: 'محمد الحربي',
      location: 'مكة المكرمة',
      rating: 5,
      comment: 'أسعار مناسبة وجودة عالية. فريق العمل محترف ومتفهم لاحتياجات العميل.',
    },
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <i
        key={index}
        className={`ri-star-${index < rating ? 'fill' : 'line'} text-yellow-400 text-lg`}
      ></i>
    ))
  }

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            آراء عملائنا
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            نفخر بثقة عملائنا وآرائهم الإيجابية التي تحفزنا على تقديم الأفضل دائماً
          </p>
        </motion.div>

        {/* Testimonials Slider */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={30}
            slidesPerView={1}
            navigation
            pagination={{ clickable: true }}
            autoplay={{ delay: 4000, disableOnInteraction: false }}
            breakpoints={{
              640: {
                slidesPerView: 1,
              },
              768: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
            }}
            className="pb-12"
          >
            {testimonials.map((testimonial) => (
              <SwiperSlide key={testimonial.id}>
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full">
                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {renderStars(testimonial.rating)}
                  </div>

                  {/* Comment */}
                  <p className="text-gray-700 leading-relaxed mb-6 text-lg">
                    "{testimonial.comment}"
                  </p>

                  {/* Customer Info */}
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div className="mr-4">
                      <h4 className="font-bold text-gray-900">
                        {testimonial.name}
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {testimonial.location}
                      </p>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16"
        >
          {[
            { number: '500+', label: 'عميل راضٍ' },
            { number: '98%', label: 'معدل الرضا' },
            { number: '15+', label: 'سنة خبرة' },
            { number: '24/7', label: 'دعم فني' },
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 text-sm md:text-base">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default Testimonials
