'use client'

import { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Pagination, Autoplay } from 'swiper/modules'
import { useSocialMedia } from '@/hooks/useSocialMedia'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

interface ProductImage {
  id: number
  image_url: string
  alt_text: string
  sort_order: number
  is_primary: number
}

interface Product {
  id: number
  title: string
  description: string
  category_name: string
  category_slug: string
  images: ProductImage[]
}

interface ProductModalProps {
  product: Product
  onClose: () => void
  type: 'kitchen' | 'cabinet'
}

const ProductModal = ({ product, onClose, type }: ProductModalProps) => {
  const { socialMedia, getWhatsAppLink, getPhoneNumber, getSocialMediaLink, getSocialMediaIcon } = useSocialMedia()

  useEffect(() => {
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden'

    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)

    return () => {
      document.body.style.overflow = 'unset'
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  // Generate social media links dynamically from database
  const generateSocialMediaLinks = () => {
    const links = []

    // WhatsApp
    const whatsappMessage = `مرحباً، أريد الاستفسار عن ${product.title}`
    links.push({
      name: 'واتساب',
      icon: getSocialMediaIcon('واتساب') || 'ri-whatsapp-line',
      url: getWhatsAppLink(whatsappMessage),
      color: 'bg-green-500 hover:bg-green-600'
    })

    // Twitter/X
    const twitterText = `شاهد هذا ${type === 'kitchen' ? 'المطبخ' : 'الخزانة'} الرائع من عجائب الخبراء: ${product.title}`
    const twitterUrl = getSocialMediaLink('تويتر')
    if (twitterUrl && twitterUrl !== '#') {
      links.push({
        name: 'تويتر',
        icon: getSocialMediaIcon('تويتر') || 'ri-twitter-x-line',
        url: twitterUrl.includes('twitter.com') ?
          `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}` :
          twitterUrl,
        color: 'bg-blue-500 hover:bg-blue-600'
      })
    }

    // Facebook
    const facebookUrl = getSocialMediaLink('فيسبوك')
    if (facebookUrl && facebookUrl !== '#') {
      links.push({
        name: 'فيسبوك',
        icon: getSocialMediaIcon('فيسبوك') || 'ri-facebook-line',
        url: facebookUrl.includes('facebook.com') ?
          `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}` :
          facebookUrl,
        color: 'bg-blue-600 hover:bg-blue-700'
      })
    }

    // Instagram
    const instagramUrl = getSocialMediaLink('إنستغرام')
    if (instagramUrl && instagramUrl !== '#') {
      links.push({
        name: 'إنستغرام',
        icon: getSocialMediaIcon('إنستغرام') || 'ri-instagram-line',
        url: instagramUrl,
        color: 'bg-pink-500 hover:bg-pink-600'
      })
    }

    // Snapchat
    const snapchatUrl = getSocialMediaLink('سناب')
    if (snapchatUrl && snapchatUrl !== '#') {
      links.push({
        name: 'سناب شات',
        icon: getSocialMediaIcon('سناب') || 'ri-snapchat-line',
        url: snapchatUrl,
        color: 'bg-yellow-500 hover:bg-yellow-600'
      })
    }

    // TikTok
    const tiktokUrl = getSocialMediaLink('تيك توك')
    if (tiktokUrl && tiktokUrl !== '#') {
      links.push({
        name: 'تيك توك',
        icon: getSocialMediaIcon('تيك توك') || 'ri-tiktok-line',
        url: tiktokUrl,
        color: 'bg-black hover:bg-gray-800'
      })
    }

    return links
  }

  const socialMediaLinks = generateSocialMediaLinks()

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/70 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="flex min-h-full items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 left-4 z-10 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
              {/* Image Gallery */}
              <div className="relative h-64 lg:h-full">
                {product.images && product.images.length > 0 ? (
                  <Swiper
                    modules={[Navigation, Pagination, Autoplay]}
                    navigation
                    pagination={{ clickable: true }}
                    autoplay={{ delay: 4000, disableOnInteraction: false }}
                    className="h-full"
                  >
                    {product.images.map((image) => (
                      <SwiperSlide key={image.id}>
                        <img
                          src={image.image_url}
                          alt={image.alt_text || product.title}
                          className="w-full h-full object-cover"
                        />
                      </SwiperSlide>
                    ))}
                  </Swiper>
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <span className="text-gray-500 text-lg">لا توجد صور</span>
                  </div>
                )}

                {/* Category Badge */}
                {product.category_name && (
                  <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-medium text-white ${
                    type === 'kitchen' ? 'bg-primary-500' : 'bg-secondary-500'
                  }`}>
                    {product.category_name}
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-6 lg:p-8 flex flex-col justify-between overflow-y-auto">
                <div>
                  {/* Title */}
                  <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                    {product.title}
                  </h2>

                  {/* Description */}
                  {product.description && (
                    <p className="text-gray-600 leading-relaxed mb-6 text-lg">
                      {product.description}
                    </p>
                  )}

                  {/* Features */}
                  <div className="mb-8">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">المميزات:</h3>
                    <ul className="space-y-2 text-gray-600">
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-primary-500 rounded-full ml-3"></span>
                        تصميم عصري وأنيق
                      </li>
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-primary-500 rounded-full ml-3"></span>
                        خامات عالية الجودة
                      </li>
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-primary-500 rounded-full ml-3"></span>
                        ضمان شامل
                      </li>
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-primary-500 rounded-full ml-3"></span>
                        تركيب احترافي
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Actions */}
                <div className="space-y-6">
                  {/* Contact Buttons */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <a
                      href={getWhatsAppLink(`مرحباً، أريد الاستفسار عن ${product.title}`)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-bold text-center transition-colors flex items-center justify-center gap-2"
                    >
                      <i className="ri-whatsapp-line text-lg"></i>
                      واتساب
                    </a>
                    <a
                      href={`tel:${getPhoneNumber()}`}
                      className={`${
                        type === 'kitchen' ? 'bg-primary-500 hover:bg-primary-600' : 'bg-secondary-500 hover:bg-secondary-600'
                      } text-white px-6 py-3 rounded-full font-bold text-center transition-colors flex items-center justify-center gap-2`}
                    >
                      <i className="ri-phone-line text-lg"></i>
                      اتصل بنا
                    </a>
                  </div>

                  {/* Social Media */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">شارك:</h4>
                    <div className="flex gap-3">
                      {socialMediaLinks.map((social) => (
                        <a
                          key={social.name}
                          href={social.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`w-10 h-10 ${social.color} text-white rounded-full flex items-center justify-center transition-colors`}
                          title={social.name}
                        >
                          <i className={`${social.icon} text-sm`}></i>
                        </a>
                      ))}
                    </div>
                  </div>

                  {/* Info */}
                  <div className="text-sm text-gray-500 border-t pt-4">
                    <p>💡 احصل على استشارة مجانية وعرض سعر مخصص</p>
                    <p>🚚 توصيل وتركيب مجاني داخل الرياض</p>
                    <p>🛡️ ضمان شامل لمدة سنتين</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}

export default ProductModal
