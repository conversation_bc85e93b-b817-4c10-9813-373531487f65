/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/footer/route";
exports.ids = ["app/api/footer/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffooter%2Froute&page=%2Fapi%2Ffooter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffooter%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffooter%2Froute&page=%2Fapi%2Ffooter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffooter%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _var_www_html_src_app_api_footer_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/footer/route.ts */ \"(rsc)/./src/app/api/footer/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/footer/route\",\n        pathname: \"/api/footer\",\n        filename: \"route\",\n        bundlePath: \"app/api/footer/route\"\n    },\n    resolvedPagePath: \"/var/www/html/src/app/api/footer/route.ts\",\n    nextConfigOutput,\n    userland: _var_www_html_src_app_api_footer_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZmb290ZXIlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmZvb3RlciUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmZvb3RlciUyRnJvdXRlLnRzJmFwcERpcj0lMkZ2YXIlMkZ3d3clMkZodG1sJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZ2YXIlMkZ3d3clMkZodG1sJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PXN0YW5kYWxvbmUmcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDUDtBQUN0RTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL3Zhci93d3cvaHRtbC9zcmMvYXBwL2FwaS9mb290ZXIvcm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwic3RhbmRhbG9uZVwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9mb290ZXIvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9mb290ZXJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2Zvb3Rlci9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi92YXIvd3d3L2h0bWwvc3JjL2FwcC9hcGkvZm9vdGVyL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffooter%2Froute&page=%2Fapi%2Ffooter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffooter%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/footer/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/footer/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n\n// GET /api/footer - Get footer data\nasync function GET() {\n    try {\n        const [socialMedia, quickLinks, contactInfo, footerSettings] = await Promise.all([\n            (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbAll)('SELECT * FROM social_media WHERE is_active = 1 ORDER BY sort_order'),\n            (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbAll)('SELECT * FROM quick_links WHERE is_active = 1 ORDER BY sort_order'),\n            (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbAll)('SELECT * FROM contact_info WHERE is_active = 1 ORDER BY sort_order'),\n            (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbGet)('SELECT * FROM footer_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1')\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            socialMedia: socialMedia || [],\n            quickLinks: quickLinks || [],\n            contactInfo: contactInfo || [],\n            settings: footerSettings || {\n                copyright_text: '© 2024 عجائب الخبراء. جميع الحقوق محفوظة.'\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching footer data:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch footer data'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/footer - Update footer data\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { socialMedia, quickLinks, contactInfo, settings } = body;\n        // Update social media\n        if (socialMedia && Array.isArray(socialMedia)) {\n            // Clear existing social media\n            await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('DELETE FROM social_media');\n            // Insert new social media\n            for(let i = 0; i < socialMedia.length; i++){\n                const item = socialMedia[i];\n                await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('INSERT INTO social_media (platform, url, icon, sort_order) VALUES (?, ?, ?, ?)', [\n                    item.platform,\n                    item.url,\n                    item.icon,\n                    i\n                ]);\n            }\n        }\n        // Update quick links\n        if (quickLinks && Array.isArray(quickLinks)) {\n            // Clear existing quick links\n            await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('DELETE FROM quick_links');\n            // Insert new quick links\n            for(let i = 0; i < quickLinks.length; i++){\n                const item = quickLinks[i];\n                await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('INSERT INTO quick_links (text, href, sort_order) VALUES (?, ?, ?)', [\n                    item.text,\n                    item.href,\n                    i\n                ]);\n            }\n        }\n        // Update contact info\n        if (contactInfo && Array.isArray(contactInfo)) {\n            // Clear existing contact info\n            await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('DELETE FROM contact_info');\n            // Insert new contact info\n            for(let i = 0; i < contactInfo.length; i++){\n                const item = contactInfo[i];\n                await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('INSERT INTO contact_info (icon, text, type, sort_order) VALUES (?, ?, ?, ?)', [\n                    item.icon,\n                    item.text,\n                    item.type || 'other',\n                    i\n                ]);\n            }\n        }\n        // Update footer settings\n        if (settings) {\n            const existingSettings = await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbGet)('SELECT id FROM footer_settings LIMIT 1');\n            if (existingSettings) {\n                await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('UPDATE footer_settings SET copyright_text = ? WHERE id = ?', [\n                    settings.copyright_text,\n                    existingSettings.id\n                ]);\n            } else {\n                await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)('INSERT INTO footer_settings (copyright_text) VALUES (?)', [\n                    settings.copyright_text\n                ]);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Footer updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating footer data:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update footer data'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/footer/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbAll: () => (/* binding */ dbAll),\n/* harmony export */   dbGet: () => (/* binding */ dbGet),\n/* harmony export */   dbRun: () => (/* binding */ dbRun),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Database connection singleton\nclass DatabaseConnection {\n    constructor(){\n        this.db = null;\n    }\n    static getInstance() {\n        if (!DatabaseConnection.instance) {\n            DatabaseConnection.instance = new DatabaseConnection();\n        }\n        return DatabaseConnection.instance;\n    }\n    async connect() {\n        if (this.db) {\n            return this.db;\n        }\n        const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'database', 'khobra_kitchens.db');\n        return new Promise((resolve, reject)=>{\n            this.db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n                if (err) {\n                    console.error('Error opening database:', err.message);\n                    reject(err);\n                } else {\n                    console.log('✅ Connected to SQLite database');\n                    resolve(this.db);\n                }\n            });\n        });\n    }\n    async close() {\n        if (this.db) {\n            return new Promise((resolve, reject)=>{\n                this.db.close((err)=>{\n                    if (err) {\n                        reject(err);\n                    } else {\n                        this.db = null;\n                        resolve();\n                    }\n                });\n            });\n        }\n    }\n    getDatabase() {\n        return this.db;\n    }\n}\n// Helper functions for database operations\nconst dbGet = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.get(query, params, (err, row)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(row);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\nconst dbAll = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.all(query, params, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(rows || []);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\nconst dbRun = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.run(query, params, function(err) {\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(this);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseConnection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlL2Nvbm5lY3Rpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBNkI7QUFDTjtBQUV2QixnQ0FBZ0M7QUFDaEMsTUFBTUU7SUFJSixhQUFzQjthQUZkQyxLQUE4QjtJQUVmO0lBRXZCLE9BQWNDLGNBQWtDO1FBQzlDLElBQUksQ0FBQ0YsbUJBQW1CRyxRQUFRLEVBQUU7WUFDaENILG1CQUFtQkcsUUFBUSxHQUFHLElBQUlIO1FBQ3BDO1FBQ0EsT0FBT0EsbUJBQW1CRyxRQUFRO0lBQ3BDO0lBRUEsTUFBYUMsVUFBcUM7UUFDaEQsSUFBSSxJQUFJLENBQUNILEVBQUUsRUFBRTtZQUNYLE9BQU8sSUFBSSxDQUFDQSxFQUFFO1FBQ2hCO1FBRUEsTUFBTUksU0FBU04sZ0RBQVMsQ0FBQ1EsUUFBUUMsR0FBRyxJQUFJLFlBQVk7UUFFcEQsT0FBTyxJQUFJQyxRQUFRLENBQUNDLFNBQVNDO1lBQzNCLElBQUksQ0FBQ1YsRUFBRSxHQUFHLElBQUlILHlEQUFnQixDQUFDTyxRQUFRLENBQUNRO2dCQUN0QyxJQUFJQSxLQUFLO29CQUNQQyxRQUFRQyxLQUFLLENBQUMsMkJBQTJCRixJQUFJRyxPQUFPO29CQUNwREwsT0FBT0U7Z0JBQ1QsT0FBTztvQkFDTEMsUUFBUUcsR0FBRyxDQUFDO29CQUNaUCxRQUFRLElBQUksQ0FBQ1QsRUFBRTtnQkFDakI7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxNQUFhaUIsUUFBdUI7UUFDbEMsSUFBSSxJQUFJLENBQUNqQixFQUFFLEVBQUU7WUFDWCxPQUFPLElBQUlRLFFBQVEsQ0FBQ0MsU0FBU0M7Z0JBQzNCLElBQUksQ0FBQ1YsRUFBRSxDQUFFaUIsS0FBSyxDQUFDLENBQUNMO29CQUNkLElBQUlBLEtBQUs7d0JBQ1BGLE9BQU9FO29CQUNULE9BQU87d0JBQ0wsSUFBSSxDQUFDWixFQUFFLEdBQUc7d0JBQ1ZTO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGO0lBRU9TLGNBQXVDO1FBQzVDLE9BQU8sSUFBSSxDQUFDbEIsRUFBRTtJQUNoQjtBQUNGO0FBRUEsMkNBQTJDO0FBQ3BDLE1BQU1tQixRQUFRLENBQUNDLE9BQWVDLFNBQWdCLEVBQUU7SUFDckQsT0FBTyxJQUFJYixRQUFRLE9BQU9DLFNBQVNDO1FBQ2pDLElBQUk7WUFDRixNQUFNVixLQUFLLE1BQU1ELG1CQUFtQkUsV0FBVyxHQUFHRSxPQUFPO1lBQ3pESCxHQUFHc0IsR0FBRyxDQUFDRixPQUFPQyxRQUFRLENBQUNULEtBQUtXO2dCQUMxQixJQUFJWCxLQUFLO29CQUNQRixPQUFPRTtnQkFDVCxPQUFPO29CQUNMSCxRQUFRYztnQkFDVjtZQUNGO1FBQ0YsRUFBRSxPQUFPVCxPQUFPO1lBQ2RKLE9BQU9JO1FBQ1Q7SUFDRjtBQUNGLEVBQUM7QUFFTSxNQUFNVSxRQUFRLENBQUNKLE9BQWVDLFNBQWdCLEVBQUU7SUFDckQsT0FBTyxJQUFJYixRQUFRLE9BQU9DLFNBQVNDO1FBQ2pDLElBQUk7WUFDRixNQUFNVixLQUFLLE1BQU1ELG1CQUFtQkUsV0FBVyxHQUFHRSxPQUFPO1lBQ3pESCxHQUFHeUIsR0FBRyxDQUFDTCxPQUFPQyxRQUFRLENBQUNULEtBQUtjO2dCQUMxQixJQUFJZCxLQUFLO29CQUNQRixPQUFPRTtnQkFDVCxPQUFPO29CQUNMSCxRQUFRaUIsUUFBUSxFQUFFO2dCQUNwQjtZQUNGO1FBQ0YsRUFBRSxPQUFPWixPQUFPO1lBQ2RKLE9BQU9JO1FBQ1Q7SUFDRjtBQUNGLEVBQUM7QUFFTSxNQUFNYSxRQUFRLENBQUNQLE9BQWVDLFNBQWdCLEVBQUU7SUFDckQsT0FBTyxJQUFJYixRQUFRLE9BQU9DLFNBQVNDO1FBQ2pDLElBQUk7WUFDRixNQUFNVixLQUFLLE1BQU1ELG1CQUFtQkUsV0FBVyxHQUFHRSxPQUFPO1lBQ3pESCxHQUFHNEIsR0FBRyxDQUFDUixPQUFPQyxRQUFRLFNBQVNULEdBQUc7Z0JBQ2hDLElBQUlBLEtBQUs7b0JBQ1BGLE9BQU9FO2dCQUNULE9BQU87b0JBQ0xILFFBQVEsSUFBSTtnQkFDZDtZQUNGO1FBQ0YsRUFBRSxPQUFPSyxPQUFPO1lBQ2RKLE9BQU9JO1FBQ1Q7SUFDRjtBQUNGLEVBQUM7QUFFRCxpRUFBZWYsa0JBQWtCQSxFQUFBIiwic291cmNlcyI6WyIvdmFyL3d3dy9odG1sL3NyYy9saWIvZGF0YWJhc2UvY29ubmVjdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgc3FsaXRlMyBmcm9tICdzcWxpdGUzJ1xuaW1wb3J0IHBhdGggZnJvbSAncGF0aCdcblxuLy8gRGF0YWJhc2UgY29ubmVjdGlvbiBzaW5nbGV0b25cbmNsYXNzIERhdGFiYXNlQ29ubmVjdGlvbiB7XG4gIHByaXZhdGUgc3RhdGljIGluc3RhbmNlOiBEYXRhYmFzZUNvbm5lY3Rpb25cbiAgcHJpdmF0ZSBkYjogc3FsaXRlMy5EYXRhYmFzZSB8IG51bGwgPSBudWxsXG5cbiAgcHJpdmF0ZSBjb25zdHJ1Y3RvcigpIHt9XG5cbiAgcHVibGljIHN0YXRpYyBnZXRJbnN0YW5jZSgpOiBEYXRhYmFzZUNvbm5lY3Rpb24ge1xuICAgIGlmICghRGF0YWJhc2VDb25uZWN0aW9uLmluc3RhbmNlKSB7XG4gICAgICBEYXRhYmFzZUNvbm5lY3Rpb24uaW5zdGFuY2UgPSBuZXcgRGF0YWJhc2VDb25uZWN0aW9uKClcbiAgICB9XG4gICAgcmV0dXJuIERhdGFiYXNlQ29ubmVjdGlvbi5pbnN0YW5jZVxuICB9XG5cbiAgcHVibGljIGFzeW5jIGNvbm5lY3QoKTogUHJvbWlzZTxzcWxpdGUzLkRhdGFiYXNlPiB7XG4gICAgaWYgKHRoaXMuZGIpIHtcbiAgICAgIHJldHVybiB0aGlzLmRiXG4gICAgfVxuXG4gICAgY29uc3QgZGJQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdkYXRhYmFzZScsICdraG9icmFfa2l0Y2hlbnMuZGInKVxuICAgIFxuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICB0aGlzLmRiID0gbmV3IHNxbGl0ZTMuRGF0YWJhc2UoZGJQYXRoLCAoZXJyKSA9PiB7XG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBvcGVuaW5nIGRhdGFiYXNlOicsIGVyci5tZXNzYWdlKVxuICAgICAgICAgIHJlamVjdChlcnIpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBDb25uZWN0ZWQgdG8gU1FMaXRlIGRhdGFiYXNlJylcbiAgICAgICAgICByZXNvbHZlKHRoaXMuZGIhKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH0pXG4gIH1cblxuICBwdWJsaWMgYXN5bmMgY2xvc2UoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgaWYgKHRoaXMuZGIpIHtcbiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgIHRoaXMuZGIhLmNsb3NlKChlcnIpID0+IHtcbiAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmRiID0gbnVsbFxuICAgICAgICAgICAgcmVzb2x2ZSgpXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBwdWJsaWMgZ2V0RGF0YWJhc2UoKTogc3FsaXRlMy5EYXRhYmFzZSB8IG51bGwge1xuICAgIHJldHVybiB0aGlzLmRiXG4gIH1cbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9ucyBmb3IgZGF0YWJhc2Ugb3BlcmF0aW9uc1xuZXhwb3J0IGNvbnN0IGRiR2V0ID0gKHF1ZXJ5OiBzdHJpbmcsIHBhcmFtczogYW55W10gPSBbXSk6IFByb21pc2U8YW55PiA9PiB7XG4gIHJldHVybiBuZXcgUHJvbWlzZShhc3luYyAocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRiID0gYXdhaXQgRGF0YWJhc2VDb25uZWN0aW9uLmdldEluc3RhbmNlKCkuY29ubmVjdCgpXG4gICAgICBkYi5nZXQocXVlcnksIHBhcmFtcywgKGVyciwgcm93KSA9PiB7XG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICByZWplY3QoZXJyKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJlc29sdmUocm93KVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZWplY3QoZXJyb3IpXG4gICAgfVxuICB9KVxufVxuXG5leHBvcnQgY29uc3QgZGJBbGwgPSAocXVlcnk6IHN0cmluZywgcGFyYW1zOiBhbnlbXSA9IFtdKTogUHJvbWlzZTxhbnlbXT4gPT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoYXN5bmMgKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYiA9IGF3YWl0IERhdGFiYXNlQ29ubmVjdGlvbi5nZXRJbnN0YW5jZSgpLmNvbm5lY3QoKVxuICAgICAgZGIuYWxsKHF1ZXJ5LCBwYXJhbXMsIChlcnIsIHJvd3MpID0+IHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgIHJlamVjdChlcnIpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmVzb2x2ZShyb3dzIHx8IFtdKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZWplY3QoZXJyb3IpXG4gICAgfVxuICB9KVxufVxuXG5leHBvcnQgY29uc3QgZGJSdW4gPSAocXVlcnk6IHN0cmluZywgcGFyYW1zOiBhbnlbXSA9IFtdKTogUHJvbWlzZTxzcWxpdGUzLlJ1blJlc3VsdD4gPT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoYXN5bmMgKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYiA9IGF3YWl0IERhdGFiYXNlQ29ubmVjdGlvbi5nZXRJbnN0YW5jZSgpLmNvbm5lY3QoKVxuICAgICAgZGIucnVuKHF1ZXJ5LCBwYXJhbXMsIGZ1bmN0aW9uKGVycikge1xuICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgcmVqZWN0KGVycilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXNvbHZlKHRoaXMpXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJlamVjdChlcnJvcilcbiAgICB9XG4gIH0pXG59XG5cbmV4cG9ydCBkZWZhdWx0IERhdGFiYXNlQ29ubmVjdGlvblxuIl0sIm5hbWVzIjpbInNxbGl0ZTMiLCJwYXRoIiwiRGF0YWJhc2VDb25uZWN0aW9uIiwiZGIiLCJnZXRJbnN0YW5jZSIsImluc3RhbmNlIiwiY29ubmVjdCIsImRiUGF0aCIsImpvaW4iLCJwcm9jZXNzIiwiY3dkIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJEYXRhYmFzZSIsImVyciIsImNvbnNvbGUiLCJlcnJvciIsIm1lc3NhZ2UiLCJsb2ciLCJjbG9zZSIsImdldERhdGFiYXNlIiwiZGJHZXQiLCJxdWVyeSIsInBhcmFtcyIsImdldCIsInJvdyIsImRiQWxsIiwiYWxsIiwicm93cyIsImRiUnVuIiwicnVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("sqlite3");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffooter%2Froute&page=%2Fapi%2Ffooter%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffooter%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();