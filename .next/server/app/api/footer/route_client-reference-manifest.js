globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/footer/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/mobile/MobileDetector.tsx":{"*":{"id":"(ssr)/./src/components/mobile/MobileDetector.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CabinetGallery.tsx":{"*":{"id":"(ssr)/./src/components/CabinetGallery.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CallToAction.tsx":{"*":{"id":"(ssr)/./src/components/CallToAction.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HeroSection.tsx":{"*":{"id":"(ssr)/./src/components/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/KitchenGallery.tsx":{"*":{"id":"(ssr)/./src/components/KitchenGallery.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar.tsx":{"*":{"id":"(ssr)/./src/components/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Testimonials.tsx":{"*":{"id":"(ssr)/./src/components/Testimonials.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WhyChooseUs.tsx":{"*":{"id":"(ssr)/./src/components/WhyChooseUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/kitchens/KitchensPageContent.tsx":{"*":{"id":"(ssr)/./src/app/kitchens/KitchensPageContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/AdminPageContent.tsx":{"*":{"id":"(ssr)/./src/app/admin/AdminPageContent.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/var/www/html/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/var/www/html/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/var/www/html/src/components/mobile/MobileDetector.tsx":{"id":"(app-pages-browser)/./src/components/mobile/MobileDetector.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/var/www/html/src/components/CabinetGallery.tsx":{"id":"(app-pages-browser)/./src/components/CabinetGallery.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/CallToAction.tsx":{"id":"(app-pages-browser)/./src/components/CallToAction.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/HeroSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/KitchenGallery.tsx":{"id":"(app-pages-browser)/./src/components/KitchenGallery.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/Navbar.tsx":{"id":"(app-pages-browser)/./src/components/Navbar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/Testimonials.tsx":{"id":"(app-pages-browser)/./src/components/Testimonials.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/src/components/WhyChooseUs.tsx":{"id":"(app-pages-browser)/./src/components/WhyChooseUs.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/var/www/html/src/app/kitchens/KitchensPageContent.tsx":{"id":"(app-pages-browser)/./src/app/kitchens/KitchensPageContent.tsx","name":"*","chunks":[],"async":false},"/var/www/html/src/app/admin/AdminPageContent.tsx":{"id":"(app-pages-browser)/./src/app/admin/AdminPageContent.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/var/www/html/src/":[],"/var/www/html/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/var/www/html/src/app/page":[{"inlined":false,"path":"static/css/app/page.css"}],"/var/www/html/src/app/api/footer/route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/mobile/MobileDetector.tsx":{"*":{"id":"(rsc)/./src/components/mobile/MobileDetector.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CabinetGallery.tsx":{"*":{"id":"(rsc)/./src/components/CabinetGallery.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CallToAction.tsx":{"*":{"id":"(rsc)/./src/components/CallToAction.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(rsc)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HeroSection.tsx":{"*":{"id":"(rsc)/./src/components/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/KitchenGallery.tsx":{"*":{"id":"(rsc)/./src/components/KitchenGallery.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navbar.tsx":{"*":{"id":"(rsc)/./src/components/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Testimonials.tsx":{"*":{"id":"(rsc)/./src/components/Testimonials.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WhyChooseUs.tsx":{"*":{"id":"(rsc)/./src/components/WhyChooseUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/kitchens/KitchensPageContent.tsx":{"*":{"id":"(rsc)/./src/app/kitchens/KitchensPageContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/AdminPageContent.tsx":{"*":{"id":"(rsc)/./src/app/admin/AdminPageContent.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}