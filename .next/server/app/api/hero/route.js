/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/hero/route";
exports.ids = ["app/api/hero/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhero%2Froute&page=%2Fapi%2Fhero%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhero%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhero%2Froute&page=%2Fapi%2Fhero%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhero%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _var_www_html_src_app_api_hero_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/hero/route.ts */ \"(rsc)/./src/app/api/hero/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/hero/route\",\n        pathname: \"/api/hero\",\n        filename: \"route\",\n        bundlePath: \"app/api/hero/route\"\n    },\n    resolvedPagePath: \"/var/www/html/src/app/api/hero/route.ts\",\n    nextConfigOutput,\n    userland: _var_www_html_src_app_api_hero_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhero%2Froute&page=%2Fapi%2Fhero%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhero%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/hero/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/hero/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n\n// GET /api/hero - Get hero section data\nasync function GET() {\n    try {\n        const hero = await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbGet)('SELECT * FROM hero_section WHERE is_active = 1 ORDER BY id DESC LIMIT 1');\n        if (!hero) {\n            // Return default hero data if none exists\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                id: null,\n                title: 'عجائب الخبراء',\n                subtitle: 'تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',\n                background_image: null,\n                primary_button_text: 'تصفح المطابخ',\n                secondary_button_text: 'تواصل معنا',\n                is_active: 1\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(hero);\n    } catch (error) {\n        console.error('Error fetching hero data:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch hero data'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/hero - Update hero section data\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { title, subtitle, background_image, primary_button_text, secondary_button_text } = body;\n        if (!title) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Title is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if hero section exists\n        const existingHero = await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbGet)('SELECT id FROM hero_section LIMIT 1');\n        if (existingHero) {\n            // Update existing hero section\n            await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)(`UPDATE hero_section \n         SET title = ?, subtitle = ?, background_image = ?, \n             primary_button_text = ?, secondary_button_text = ?\n         WHERE id = ?`, [\n                title,\n                subtitle,\n                background_image,\n                primary_button_text,\n                secondary_button_text,\n                existingHero.id\n            ]);\n        } else {\n            // Create new hero section\n            await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)(`INSERT INTO hero_section (title, subtitle, background_image, primary_button_text, secondary_button_text)\n         VALUES (?, ?, ?, ?, ?)`, [\n                title,\n                subtitle,\n                background_image,\n                primary_button_text,\n                secondary_button_text\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Hero section updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating hero data:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update hero data'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/hero/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbAll: () => (/* binding */ dbAll),\n/* harmony export */   dbGet: () => (/* binding */ dbGet),\n/* harmony export */   dbRun: () => (/* binding */ dbRun),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Database connection singleton\nclass DatabaseConnection {\n    constructor(){\n        this.db = null;\n    }\n    static getInstance() {\n        if (!DatabaseConnection.instance) {\n            DatabaseConnection.instance = new DatabaseConnection();\n        }\n        return DatabaseConnection.instance;\n    }\n    async connect() {\n        if (this.db) {\n            return this.db;\n        }\n        const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'database', 'khobra_kitchens.db');\n        return new Promise((resolve, reject)=>{\n            this.db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n                if (err) {\n                    console.error('Error opening database:', err.message);\n                    reject(err);\n                } else {\n                    console.log('✅ Connected to SQLite database');\n                    resolve(this.db);\n                }\n            });\n        });\n    }\n    async close() {\n        if (this.db) {\n            return new Promise((resolve, reject)=>{\n                this.db.close((err)=>{\n                    if (err) {\n                        reject(err);\n                    } else {\n                        this.db = null;\n                        resolve();\n                    }\n                });\n            });\n        }\n    }\n    getDatabase() {\n        return this.db;\n    }\n}\n// Helper functions for database operations\nconst dbGet = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.get(query, params, (err, row)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(row);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\nconst dbAll = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.all(query, params, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(rows || []);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\nconst dbRun = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.run(query, params, function(err) {\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(this);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseConnection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("sqlite3");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhero%2Froute&page=%2Fapi%2Fhero%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhero%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();