/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cabinets/route";
exports.ids = ["app/api/cabinets/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcabinets%2Froute&page=%2Fapi%2Fcabinets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcabinets%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcabinets%2Froute&page=%2Fapi%2Fcabinets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcabinets%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _var_www_html_src_app_api_cabinets_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/cabinets/route.ts */ \"(rsc)/./src/app/api/cabinets/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cabinets/route\",\n        pathname: \"/api/cabinets\",\n        filename: \"route\",\n        bundlePath: \"app/api/cabinets/route\"\n    },\n    resolvedPagePath: \"/var/www/html/src/app/api/cabinets/route.ts\",\n    nextConfigOutput,\n    userland: _var_www_html_src_app_api_cabinets_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcabinets%2Froute&page=%2Fapi%2Fcabinets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcabinets%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/cabinets/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/cabinets/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n\n// GET /api/cabinets - Get all cabinets\nasync function GET() {\n    try {\n        const cabinets = await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbAll)(`\n      SELECT c.*, cat.name as category_name, cat.slug as category_slug\n      FROM cabinets c\n      LEFT JOIN categories cat ON c.category_id = cat.id\n      WHERE c.is_active = 1\n      ORDER BY c.sort_order, c.id\n    `);\n        // Get images for each cabinet\n        for (const cabinet of cabinets){\n            cabinet.images = await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbAll)('SELECT * FROM cabinet_images WHERE cabinet_id = ? ORDER BY sort_order', [\n                cabinet.id\n            ]);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(cabinets);\n    } catch (error) {\n        console.error('Error fetching cabinets:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch cabinets'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/cabinets - Add new cabinet\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { title, description, category_id, is_featured = 0, sort_order = 0 } = body;\n        if (!title) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Title is required'\n            }, {\n                status: 400\n            });\n        }\n        const result = await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_1__.dbRun)(`INSERT INTO cabinets (title, description, category_id, is_featured, sort_order)\n       VALUES (?, ?, ?, ?, ?)`, [\n            title,\n            description,\n            category_id,\n            is_featured,\n            sort_order\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            id: result.lastID,\n            title,\n            description,\n            category_id,\n            is_featured,\n            sort_order,\n            message: 'Cabinet added successfully'\n        });\n    } catch (error) {\n        console.error('Error adding cabinet:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to add cabinet'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cabinets/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbAll: () => (/* binding */ dbAll),\n/* harmony export */   dbGet: () => (/* binding */ dbGet),\n/* harmony export */   dbRun: () => (/* binding */ dbRun),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Database connection singleton\nclass DatabaseConnection {\n    constructor(){\n        this.db = null;\n    }\n    static getInstance() {\n        if (!DatabaseConnection.instance) {\n            DatabaseConnection.instance = new DatabaseConnection();\n        }\n        return DatabaseConnection.instance;\n    }\n    async connect() {\n        if (this.db) {\n            return this.db;\n        }\n        const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'database', 'khobra_kitchens.db');\n        return new Promise((resolve, reject)=>{\n            this.db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n                if (err) {\n                    console.error('Error opening database:', err.message);\n                    reject(err);\n                } else {\n                    console.log('✅ Connected to SQLite database');\n                    resolve(this.db);\n                }\n            });\n        });\n    }\n    async close() {\n        if (this.db) {\n            return new Promise((resolve, reject)=>{\n                this.db.close((err)=>{\n                    if (err) {\n                        reject(err);\n                    } else {\n                        this.db = null;\n                        resolve();\n                    }\n                });\n            });\n        }\n    }\n    getDatabase() {\n        return this.db;\n    }\n}\n// Helper functions for database operations\nconst dbGet = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.get(query, params, (err, row)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(row);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\nconst dbAll = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.all(query, params, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(rows || []);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\nconst dbRun = (query, params = [])=>{\n    return new Promise(async (resolve, reject)=>{\n        try {\n            const db = await DatabaseConnection.getInstance().connect();\n            db.run(query, params, function(err) {\n                if (err) {\n                    reject(err);\n                } else {\n                    resolve(this);\n                }\n            });\n        } catch (error) {\n            reject(error);\n        }\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatabaseConnection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("sqlite3");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcabinets%2Froute&page=%2Fapi%2Fcabinets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcabinets%2Froute.ts&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();