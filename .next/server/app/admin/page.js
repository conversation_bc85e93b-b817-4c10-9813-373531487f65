/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(rsc)/./src/app/admin/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/var/www/html/src/app/admin/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.webmanifest\"\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/var/www/html/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.webmanifest\"\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/var/www/html/src/app/admin/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fcomponents%2Fmobile%2FMobileDetector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fcomponents%2Fmobile%2FMobileDetector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/mobile/MobileDetector.tsx */ \"(rsc)/./src/components/mobile/MobileDetector.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnZhciUyRnd3dyUyRmh0bWwlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMlRhamF3YWwlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIyYXJhYmljJTVDJTIyJTJDJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ3ZWlnaHQlNUMlMjIlM0ElNUIlNUMlMjIyMDAlNUMlMjIlMkMlNUMlMjIzMDAlNUMlMjIlMkMlNUMlMjI0MDAlNUMlMjIlMkMlNUMlMjI1MDAlNUMlMjIlMkMlNUMlMjI3MDAlNUMlMjIlMkMlNUMlMjI4MDAlNUMlMjIlMkMlNUMlMjI5MDAlNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC10YWphd2FsJTVDJTIyJTJDJTVDJTIyZGlzcGxheSU1QyUyMiUzQSU1QyUyMnN3YXAlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJ0YWphd2FsJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnZhciUyRnd3dyUyRmh0bWwlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ2YXIlMkZ3d3clMkZodG1sJTJGc3JjJTJGY29tcG9uZW50cyUyRm1vYmlsZSUyRk1vYmlsZURldGVjdG9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUF3SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi92YXIvd3d3L2h0bWwvc3JjL2NvbXBvbmVudHMvbW9iaWxlL01vYmlsZURldGVjdG9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fcomponents%2Fmobile%2FMobileDetector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fadmin%2FAdminPageContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fadmin%2FAdminPageContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/AdminPageContent.tsx */ \"(rsc)/./src/app/admin/AdminPageContent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnZhciUyRnd3dyUyRmh0bWwlMkZzcmMlMkZhcHAlMkZhZG1pbiUyRkFkbWluUGFnZUNvbnRlbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL3Zhci93d3cvaHRtbC9zcmMvYXBwL2FkbWluL0FkbWluUGFnZUNvbnRlbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fadmin%2FAdminPageContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/AdminPageContent.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AdminPageContent.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/var/www/html/src/app/admin/AdminPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/var/www/html/src/app/admin/AdminPageContent.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _AdminPageContent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AdminPageContent */ \"(rsc)/./src/app/admin/AdminPageContent.tsx\");\n\n\nconst metadata = {\n    title: 'لوحة الإدارة - عجائب الخبراء',\n    description: 'لوحة إدارة موقع عجائب الخبراء - منطقة محظورة للمديرين فقط',\n    robots: 'noindex, nofollow, noarchive, nosnippet'\n};\nfunction AdminPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminPageContent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/var/www/html/src/app/admin/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FkbWluL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUNpRDtBQUUxQyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFFBQVE7QUFDVixFQUFDO0FBRWMsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNMLHlEQUFnQkE7Ozs7O0FBQzFCIiwic291cmNlcyI6WyIvdmFyL3d3dy9odG1sL3NyYy9hcHAvYWRtaW4vcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgQWRtaW5QYWdlQ29udGVudCBmcm9tICcuL0FkbWluUGFnZUNvbnRlbnQnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn2YTZiNit2Kkg2KfZhNil2K/Yp9ix2KkgLSDYudis2KfYptioINin2YTYrtio2LHYp9ihJyxcbiAgZGVzY3JpcHRpb246ICfZhNmI2K3YqSDYpdiv2KfYsdipINmF2YjZgti5INi52KzYp9im2Kgg2KfZhNiu2KjYsdin2KEgLSDZhdmG2LfZgtipINmF2K3YuNmI2LHYqSDZhNmE2YXYr9mK2LHZitmGINmB2YLYtycsXG4gIHJvYm90czogJ25vaW5kZXgsIG5vZm9sbG93LCBub2FyY2hpdmUsIG5vc25pcHBldCcsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluUGFnZSgpIHtcbiAgcmV0dXJuIDxBZG1pblBhZ2VDb250ZW50IC8+XG59XG4iXSwibmFtZXMiOlsiQWRtaW5QYWdlQ29udGVudCIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInJvYm90cyIsIkFkbWluUGFnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/admin/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f4e217b3c531\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvdmFyL3d3dy9odG1sL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmNGUyMTdiM2M1MzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_200_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Tajawal\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_200_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_200_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_mobile_MobileDetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/mobile/MobileDetector */ \"(rsc)/./src/components/mobile/MobileDetector.tsx\");\n\n\n\n\nconst metadata = {\n    title: {\n        default: 'عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',\n        template: '%s | عجائب الخبراء'\n    },\n    description: 'شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية والفاخرة بأعلى معايير الجودة والحرفية في المملكة العربية السعودية. احصل على مطبخ أحلامك اليوم!',\n    keywords: [\n        'مطابخ السعودية',\n        'تصميم مطابخ',\n        'مطابخ عصرية',\n        'مطابخ كلاسيكية',\n        'خزائن ملابس',\n        'تفصيل مطابخ',\n        'مطابخ فاخرة',\n        'عجائب الخبراء',\n        'مطابخ الرياض'\n    ],\n    authors: [\n        {\n            name: 'عجائب الخبراء'\n        }\n    ],\n    creator: 'عجائب الخبراء',\n    publisher: 'عجائب الخبراء',\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://khobrakitchens.com'),\n    alternates: {\n        canonical: '/'\n    },\n    openGraph: {\n        title: 'عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',\n        description: 'شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية والفاخرة بأعلى معايير الجودة والحرفية في المملكة العربية السعودية.',\n        url: 'https://khobrakitchens.com',\n        siteName: 'عجائب الخبراء',\n        locale: 'ar_SA',\n        type: 'website'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'عجائب الخبراء - تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية',\n        description: 'شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية والفاخرة بأعلى معايير الجودة والحرفية في المملكة العربية السعودية.'\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    verification: {\n        google: 'your-google-verification-code'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_200_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_3___default().variable),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Tajawal_arguments_subsets_arabic_latin_weight_200_300_400_500_700_800_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_3___default().className)} antialiased bg-gray-50`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_MobileDetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/app/layout.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/var/www/html/src/app/layout.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/var/www/html/src/app/layout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/mobile/MobileDetector.tsx":
/*!**************************************************!*\
  !*** ./src/components/mobile/MobileDetector.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/var/www/html/src/components/mobile/MobileDetector.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/var/www/html/src/components/mobile/MobileDetector.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fcomponents%2Fmobile%2FMobileDetector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fcomponents%2Fmobile%2FMobileDetector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/mobile/MobileDetector.tsx */ \"(ssr)/./src/components/mobile/MobileDetector.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnZhciUyRnd3dyUyRmh0bWwlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMlRhamF3YWwlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIyYXJhYmljJTVDJTIyJTJDJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ3ZWlnaHQlNUMlMjIlM0ElNUIlNUMlMjIyMDAlNUMlMjIlMkMlNUMlMjIzMDAlNUMlMjIlMkMlNUMlMjI0MDAlNUMlMjIlMkMlNUMlMjI1MDAlNUMlMjIlMkMlNUMlMjI3MDAlNUMlMjIlMkMlNUMlMjI4MDAlNUMlMjIlMkMlNUMlMjI5MDAlNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC10YWphd2FsJTVDJTIyJTJDJTVDJTIyZGlzcGxheSU1QyUyMiUzQSU1QyUyMnN3YXAlNUMlMjIlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJ0YWphd2FsJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnZhciUyRnd3dyUyRmh0bWwlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ2YXIlMkZ3d3clMkZodG1sJTJGc3JjJTJGY29tcG9uZW50cyUyRm1vYmlsZSUyRk1vYmlsZURldGVjdG9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUF3SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi92YXIvd3d3L2h0bWwvc3JjL2NvbXBvbmVudHMvbW9iaWxlL01vYmlsZURldGVjdG9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Tajawal%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%2C%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22200%5C%22%2C%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fcomponents%2Fmobile%2FMobileDetector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fadmin%2FAdminPageContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fadmin%2FAdminPageContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/AdminPageContent.tsx */ \"(ssr)/./src/app/admin/AdminPageContent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnZhciUyRnd3dyUyRmh0bWwlMkZzcmMlMkZhcHAlMkZhZG1pbiUyRkFkbWluUGFnZUNvbnRlbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQWtIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL3Zhci93d3cvaHRtbC9zcmMvYXBwL2FkbWluL0FkbWluUGFnZUNvbnRlbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp%2Fadmin%2FAdminPageContent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/AdminPageContent.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AdminPageContent.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_admin_AdminLogin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLogin */ \"(ssr)/./src/components/admin/AdminLogin.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst AdminPageContent = ()=>{\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPageContent.useEffect\": ()=>{\n            // Check if user is already authenticated\n            const checkAuth = {\n                \"AdminPageContent.useEffect.checkAuth\": ()=>{\n                    const token = localStorage.getItem('admin_token');\n                    const expiry = localStorage.getItem('admin_token_expiry');\n                    if (token && expiry) {\n                        const now = new Date().getTime();\n                        if (now < parseInt(expiry)) {\n                            setIsAuthenticated(true);\n                        } else {\n                            // Token expired, remove it\n                            localStorage.removeItem('admin_token');\n                            localStorage.removeItem('admin_token_expiry');\n                        }\n                    }\n                    setLoading(false);\n                }\n            }[\"AdminPageContent.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AdminPageContent.useEffect\"], []);\n    const handleLogin = (success)=>{\n        if (success) {\n            setIsAuthenticated(true);\n            // Set token with 24 hour expiry\n            const expiry = new Date().getTime() + 24 * 60 * 60 * 1000;\n            localStorage.setItem('admin_token', 'authenticated');\n            localStorage.setItem('admin_token_expiry', expiry.toString());\n        }\n    };\n    const handleLogout = ()=>{\n        setIsAuthenticated(false);\n        localStorage.removeItem('admin_token');\n        localStorage.removeItem('admin_token_expiry');\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 flex items-center justify-center\",\n            dir: \"rtl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500\"\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30\",\n        dir: \"rtl\",\n        children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"لوحة الإدارة\"\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"مرحباً بك في لوحة إدارة عجائب الخبراء\"\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleLogout,\n                    className: \"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\",\n                    children: \"تسجيل الخروج\"\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n            lineNumber: 60,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLogin__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            onLogin: handleLogin\n        }, void 0, false, {\n            fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n            lineNumber: 71,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/var/www/html/src/app/admin/AdminPageContent.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminPageContent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/AdminPageContent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/AdminLogin.tsx":
/*!*********************************************!*\
  !*** ./src/components/admin/AdminLogin.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst AdminLogin = ({ onLogin })=>{\n    const [credentials, setCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: ''\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        // Simple authentication (in production, this should be server-side)\n        if (credentials.username === 'admin' && credentials.password === 'admin123') {\n            onLogin(true);\n        } else {\n            setError('اسم المستخدم أو كلمة المرور غير صحيحة');\n            onLogin(false);\n        }\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full animate-fade-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-bold text-2xl\",\n                                children: \"ع\"\n                            }, void 0, false, {\n                                fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"لوحة الإدارة\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"عجائب الخبراء - نظام إدارة المحتوى\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"اسم المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"username\",\n                                            value: credentials.username,\n                                            onChange: (e)=>setCredentials({\n                                                    ...credentials,\n                                                    username: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors\",\n                                            placeholder: \"أدخل اسم المستخدم\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            id: \"password\",\n                                            value: credentials.password,\n                                            onChange: (e)=>setCredentials({\n                                                    ...credentials,\n                                                    password: e.target.value\n                                                }),\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors\",\n                                            placeholder: \"أدخل كلمة المرور\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"جاري تسجيل الدخول...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, undefined) : 'تسجيل الدخول'\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: \"بيانات تجريبية:\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"المستخدم:\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" admin\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 47\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"كلمة المرور:\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" admin123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8 text-sm text-gray-500\",\n                    children: \"\\xa9 2024 عجائب الخبراء. جميع الحقوق محفوظة.\"\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/var/www/html/src/components/admin/AdminLogin.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminLogin);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/AdminLogin.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileAboutPage.tsx":
/*!***************************************************!*\
  !*** ./src/components/mobile/MobileAboutPage.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileAboutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useSocialMedia */ \"(ssr)/./src/hooks/useSocialMedia.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MobileAboutPage() {\n    const { getWhatsAppLink, getPhoneNumber } = (0,_hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_1__.useSocialMedia)();\n    const values = [\n        {\n            icon: 'ri-award-line',\n            title: 'الجودة',\n            description: 'نستخدم أفضل المواد والخامات لضمان جودة عالية ومتانة طويلة الأمد',\n            color: 'from-blue-500 to-blue-600'\n        },\n        {\n            icon: 'ri-customer-service-line',\n            title: 'خدمة العملاء',\n            description: 'نضع رضا العملاء في المقدمة ونسعى لتجاوز توقعاتهم',\n            color: 'from-green-500 to-green-600'\n        },\n        {\n            icon: 'ri-lightbulb-line',\n            title: 'الإبداع',\n            description: 'نقدم تصاميم مبتكرة وحلول عملية تناسب احتياجات كل عميل',\n            color: 'from-orange-500 to-orange-600'\n        },\n        {\n            icon: 'ri-shield-check-line',\n            title: 'الثقة',\n            description: 'نبني علاقات طويلة الأمد مع عملائنا قائمة على الثقة والشفافية',\n            color: 'from-purple-500 to-purple-600'\n        }\n    ];\n    const stats = [\n        {\n            number: '15+',\n            label: 'سنة خبرة',\n            icon: 'ri-time-line'\n        },\n        {\n            number: '500+',\n            label: 'مشروع منجز',\n            icon: 'ri-building-line'\n        },\n        {\n            number: '100%',\n            label: 'رضا العملاء',\n            icon: 'ri-heart-line'\n        },\n        {\n            number: '24/7',\n            label: 'دعم فني',\n            icon: 'ri-customer-service-line'\n        }\n    ];\n    const team = [\n        {\n            icon: 'ri-pencil-ruler-line',\n            title: 'فريق التصميم',\n            description: 'مصممون محترفون متخصصون في إبداع تصاميم عصرية وكلاسيكية',\n            count: '10+'\n        },\n        {\n            icon: 'ri-hammer-line',\n            title: 'فريق التنفيذ',\n            description: 'حرفيون مهرة يتمتعون بخبرة واسعة في تنفيذ المشاريع',\n            count: '25+'\n        },\n        {\n            icon: 'ri-customer-service-2-line',\n            title: 'فريق خدمة العملاء',\n            description: 'متخصصون في خدمة العملاء يضمنون تجربة مميزة',\n            count: '5+'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-kitchen-600 to-kitchen-800 pt-8 pb-12 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-3\",\n                            children: \"من نحن\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-kitchen-100 leading-relaxed text-sm\",\n                            children: \"شركة عجائب الخبراء الرائدة في تصميم وتنفيذ المطابخ والخزائن العصرية والكلاسيكية\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 -mt-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-lg flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: `${stat.icon} text-white text-lg`\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold text-kitchen-600 mb-1\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600 text-xs\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold text-gray-900 mb-3 text-center\",\n                            children: \"قصتنا\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 text-gray-700 leading-relaxed text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"بدأت رحلة عجائب الخبراء منذ أكثر من 15 عاماً برؤية واضحة: تحويل المطابخ والخزائن من مجرد مساحات وظيفية إلى تحف فنية تجمع بين الجمال والعملية.\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"نحن نؤمن بأن المطبخ هو قلب المنزل، والخزائن هي روح التنظيم. لذلك نسعى دائماً لتقديم حلول مبتكرة تلبي احتياجات عملائنا وتفوق توقعاتهم.\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-lg flex items-center justify-center ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-eye-line text-white text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: \"رؤيتنا\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm\",\n                                    children: \"أن نكون الشركة الرائدة في تصميم وتنفيذ المطابخ والخزائن في المملكة العربية السعودية، ونساهم في تحسين جودة الحياة من خلال إبداعاتنا.\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-warm-500 to-warm-600 rounded-lg flex items-center justify-center ml-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-target-line text-white text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: \"رسالتنا\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed text-sm\",\n                                    children: \"نلتزم بتقديم خدمات متميزة في تصميم وتنفيذ المطابخ والخزائن باستخدام أحدث التقنيات وأفضل المواد، مع ضمان رضا العملاء والالتزام بالمواعيد.\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold text-gray-900 text-center mb-4\",\n                        children: \"قيمنا\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-3\",\n                        children: values.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-10 h-10 bg-gradient-to-br ${value.color} rounded-lg flex items-center justify-center ml-3 flex-shrink-0`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: `${value.icon} text-white text-lg`\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-bold text-gray-900 mb-1\",\n                                                    children: value.title\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-xs leading-relaxed\",\n                                                    children: value.description\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold text-gray-900 text-center mb-4\",\n                        children: \"فريق العمل\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: team.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-lg flex items-center justify-center ml-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: `${member.icon} text-white text-lg`\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-base font-bold text-gray-900\",\n                                                            children: member.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-kitchen-600 font-bold text-sm\",\n                                                            children: member.count\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-xs leading-relaxed\",\n                                        children: member.description\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-kitchen-600 to-kitchen-800 rounded-xl p-4 text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold mb-2\",\n                            children: \"جاهز لبدء مشروعك معنا؟\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-kitchen-100 mb-4 text-xs\",\n                            children: \"تواصل معنا اليوم واحصل على استشارة مجانية وعرض سعر مخصص لمشروعك\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: getWhatsAppLink(),\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"block bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg font-medium transition-colors duration-300 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-whatsapp-line text-lg ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تواصل عبر واتساب\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `tel:${getPhoneNumber()}`,\n                                    className: \"block bg-white text-kitchen-600 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-300 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-phone-line text-lg ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"اتصل بنا الآن\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/src/components/mobile/MobileAboutPage.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileAboutPage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileCabinetsPage.tsx":
/*!******************************************************!*\
  !*** ./src/components/mobile/MobileCabinetsPage.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileCabinetsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocialMedia */ \"(ssr)/./src/hooks/useSocialMedia.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MobileCabinetsPage() {\n    const { getWhatsAppLink, getSocialMediaLink } = (0,_hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_2__.useSocialMedia)();\n    const [cabinets, setCabinets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedCabinet, setSelectedCabinet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MobileCabinetsPage.useEffect\": ()=>{\n            fetchCabinets();\n        }\n    }[\"MobileCabinetsPage.useEffect\"], []);\n    const fetchCabinets = async ()=>{\n        try {\n            const response = await fetch('/api/cabinets');\n            if (response.ok) {\n                const data = await response.json();\n                setCabinets(data);\n            }\n        } catch (error) {\n            console.error('Error fetching cabinets:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const openModal = (cabinet)=>{\n        setSelectedCabinet(cabinet);\n        setCurrentImageIndex(0);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeModal = ()=>{\n        setSelectedCabinet(null);\n        setCurrentImageIndex(0);\n        document.body.style.overflow = 'unset';\n    };\n    const nextImage = ()=>{\n        if (selectedCabinet) {\n            setCurrentImageIndex((prev)=>prev === selectedCabinet.images.length - 1 ? 0 : prev + 1);\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedCabinet) {\n            setCurrentImageIndex((prev)=>prev === 0 ? selectedCabinet.images.length - 1 : prev - 1);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري تحميل الخزانات...\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-accent-600 to-accent-800 pt-8 pb-6 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"الخزانات\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-accent-100\",\n                            children: \"حلول تخزين ذكية ومميزة\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: cabinets.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-archive-line text-4xl text-gray-400 mb-3\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-base\",\n                            children: \"لا توجد خزانات متاحة حالياً\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: cabinets.map((cabinet)=>{\n                        const firstImage = cabinet.images && cabinet.images.length > 0 ? cabinet.images[0].image_url : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\",\n                            onClick: ()=>openModal(cabinet),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: firstImage,\n                                            alt: cabinet.title,\n                                            className: \"w-full h-full object-cover transition-transform duration-300 hover:scale-105\",\n                                            onError: (e)=>{\n                                                e.currentTarget.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 bg-accent-600 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                            children: cabinet.category_name || 'خزانة'\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 21\n                                        }, this),\n                                        cabinet.images && cabinet.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs\",\n                                            children: [\n                                                \"+\",\n                                                cabinet.images.length - 1,\n                                                \" صور\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-2\",\n                                            children: cabinet.title\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-3 line-clamp-2 text-sm\",\n                                            children: cabinet.description\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full bg-accent-600 text-white py-2 rounded-lg font-medium hover:bg-accent-700 transition-colors duration-300 text-sm\",\n                                            children: \"عرض التفاصيل\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, cabinet.id, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            selectedCabinet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/90 z-50 flex items-end justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-t-2xl w-full h-[95vh] overflow-hidden modal-slide-up flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-gray-900\",\n                                    children: selectedCabinet.title\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    className: \"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-close-line text-lg text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 overflow-hidden\",\n                                    children: selectedCabinet.images && selectedCabinet.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedCabinet.images[currentImageIndex]?.image_url || 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',\n                                        alt: selectedCabinet.title,\n                                        className: \"w-full h-full object-cover\",\n                                        onError: (e)=>{\n                                            e.currentTarget.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop\",\n                                        alt: selectedCabinet.title,\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this),\n                                selectedCabinet.images && selectedCabinet.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-arrow-left-s-line text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-arrow-right-s-line text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1\",\n                                            children: selectedCabinet.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentImageIndex(index),\n                                                    className: `w-2 h-2 rounded-full transition-all duration-300 ${index === currentImageIndex ? 'bg-white scale-125' : 'bg-white/50'}`\n                                                }, index, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-accent-100 text-accent-800 px-3 py-1 rounded-full text-sm font-medium\",\n                                        children: selectedCabinet.category_name || 'خزانة'\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: selectedCabinet.description\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-4\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-archive-line text-accent-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"تخزين ذكي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-ruler-line text-accent-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"مقاسات مخصصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-shield-check-line text-accent-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"ضمان شامل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-tools-line text-accent-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"تركيب مجاني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-4 text-center\",\n                                            children: \"تابعنا على\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('snapchat'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white hover:bg-yellow-600 transition-colors duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-snapchat-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('instagram'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-instagram-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('tiktok'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-tiktok-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('twitter'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-twitter-x-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t bg-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.open(getWhatsAppLink(), '_blank'),\n                                        className: \"w-full bg-green-500 text-white py-3 rounded-xl font-medium hover:bg-green-600 transition-colors duration-300 flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-whatsapp-line text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"تواصل عبر واتساب\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: closeModal,\n                                        className: \"w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors duration-300\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/src/components/mobile/MobileCabinetsPage.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileCabinetsPage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileContactPage.tsx":
/*!*****************************************************!*\
  !*** ./src/components/mobile/MobileContactPage.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocialMedia */ \"(ssr)/./src/hooks/useSocialMedia.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MobileContactPage() {\n    const { getWhatsAppLink, getPhoneNumber, getSocialMediaLink, getSocialMediaIcon } = (0,_hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_2__.useSocialMedia)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        phone: '',\n        email: '',\n        service: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const services = [\n        'تصميم مطابخ عصرية',\n        'تصميم مطابخ كلاسيكية',\n        'تصميم خزانات ملابس',\n        'تصميم خزانات مطبخ',\n        'استشارة تصميم',\n        'صيانة وتجديد',\n        'أخرى'\n    ];\n    const contactMethods = [\n        {\n            icon: 'ri-whatsapp-line',\n            title: 'واتساب',\n            value: getPhoneNumber(),\n            action: ()=>window.open(getWhatsAppLink(), '_blank'),\n            color: 'from-green-500 to-green-600'\n        },\n        {\n            icon: 'ri-phone-line',\n            title: 'اتصال مباشر',\n            value: getPhoneNumber(),\n            action: ()=>window.open(`tel:${getPhoneNumber()}`, '_self'),\n            color: 'from-blue-500 to-blue-600'\n        },\n        {\n            icon: 'ri-mail-line',\n            title: 'البريد الإلكتروني',\n            value: '<EMAIL>',\n            action: ()=>window.open('mailto:<EMAIL>', '_self'),\n            color: 'from-purple-500 to-purple-600'\n        }\n    ];\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            const response = await fetch('/api/contact', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            if (response.ok) {\n                setSubmitStatus('success');\n                setFormData({\n                    name: '',\n                    phone: '',\n                    email: '',\n                    service: '',\n                    message: ''\n                });\n            } else {\n                setSubmitStatus('error');\n            }\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-kitchen-600 to-kitchen-800 pt-8 pb-6 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"تواصل معنا\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-kitchen-100 text-sm\",\n                            children: \"نحن هنا لمساعدتك في تحقيق حلمك\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 -mt-3 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base font-bold text-gray-900 mb-3 text-center\",\n                            children: \"طرق التواصل السريع\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: contactMethods.map((method, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: method.action,\n                                    className: `w-full bg-gradient-to-r ${method.color} text-white p-3 rounded-lg flex items-center justify-between hover:shadow-lg transition-all duration-300`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: `${method.icon} text-lg ml-2`\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold text-sm\",\n                                                            children: method.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs opacity-90\",\n                                                            children: method.value\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-arrow-left-s-line text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base font-bold text-gray-900 mb-4 text-center\",\n                            children: \"أرسل لنا رسالة\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 font-medium mb-1 text-sm\",\n                                            children: \"الاسم الكامل *\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            name: \"name\",\n                                            value: formData.name,\n                                            onChange: handleInputChange,\n                                            required: true,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm\",\n                                            placeholder: \"أدخل اسمك الكامل\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 font-medium mb-1 text-sm\",\n                                            children: \"رقم الهاتف *\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"tel\",\n                                            name: \"phone\",\n                                            value: formData.phone,\n                                            onChange: handleInputChange,\n                                            required: true,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm\",\n                                            placeholder: \"05xxxxxxxx\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 font-medium mb-1 text-sm\",\n                                            children: \"البريد الإلكتروني\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: formData.email,\n                                            onChange: handleInputChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm\",\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 font-medium mb-1 text-sm\",\n                                            children: \"نوع الخدمة المطلوبة\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"service\",\n                                            value: formData.service,\n                                            onChange: handleInputChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر نوع الخدمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: service,\n                                                        children: service\n                                                    }, index, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 font-medium mb-1 text-sm\",\n                                            children: \"الرسالة *\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            name: \"message\",\n                                            value: formData.message,\n                                            onChange: handleInputChange,\n                                            required: true,\n                                            rows: 3,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kitchen-500 focus:border-transparent transition-all duration-300 resize-none text-sm\",\n                                            placeholder: \"اكتب رسالتك هنا...\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"w-full bg-kitchen-600 text-white py-3 rounded-lg font-bold text-sm hover:bg-kitchen-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300\",\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"جاري الإرسال...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this) : 'إرسال الرسالة'\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-100 border border-green-400 text-green-700 px-3 py-2 rounded-lg text-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-check-line text-lg ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded-lg text-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"ri-error-warning-line text-lg ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base font-bold text-gray-900 mb-3 text-center\",\n                            children: \"تابعنا على\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: getSocialMediaLink('snapchat'),\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center text-white hover:bg-yellow-600 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-snapchat-line text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: getSocialMediaLink('instagram'),\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-instagram-line text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: getSocialMediaLink('tiktok'),\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"w-12 h-12 bg-black rounded-lg flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-tiktok-line text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: getSocialMediaLink('twitter'),\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"w-12 h-12 bg-black rounded-lg flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-twitter-x-line text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base font-bold text-gray-900 mb-3 text-center\",\n                            children: \"موقعنا\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-gray-600 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ri-map-pin-line text-xl text-kitchen-600 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"المملكة العربية السعودية\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"الرياض\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 rounded-lg h-32 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-map-line text-2xl mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"خريطة الموقع\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/src/components/mobile/MobileContactPage.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileContactPage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileDetector.tsx":
/*!**************************************************!*\
  !*** ./src/components/mobile/MobileDetector.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileDetector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _MobileLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MobileLayout */ \"(ssr)/./src/components/mobile/MobileLayout.tsx\");\n/* harmony import */ var _MobileHomePage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileHomePage */ \"(ssr)/./src/components/mobile/MobileHomePage.tsx\");\n/* harmony import */ var _MobileKitchensPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MobileKitchensPage */ \"(ssr)/./src/components/mobile/MobileKitchensPage.tsx\");\n/* harmony import */ var _MobileCabinetsPage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MobileCabinetsPage */ \"(ssr)/./src/components/mobile/MobileCabinetsPage.tsx\");\n/* harmony import */ var _MobileAboutPage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MobileAboutPage */ \"(ssr)/./src/components/mobile/MobileAboutPage.tsx\");\n/* harmony import */ var _MobileContactPage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MobileContactPage */ \"(ssr)/./src/components/mobile/MobileContactPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction MobileDetector({ children }) {\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MobileDetector.useEffect\": ()=>{\n            const checkDevice = {\n                \"MobileDetector.useEffect.checkDevice\": ()=>{\n                    const userAgent = navigator.userAgent.toLowerCase();\n                    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);\n                    const isSmallScreen = window.innerWidth <= 768;\n                    setIsMobile(isMobileDevice || isSmallScreen);\n                    setIsLoading(false);\n                }\n            }[\"MobileDetector.useEffect.checkDevice\"];\n            // Check on mount\n            checkDevice();\n            // Check on resize\n            const handleResize = {\n                \"MobileDetector.useEffect.handleResize\": ()=>{\n                    const isSmallScreen = window.innerWidth <= 768;\n                    const userAgent = navigator.userAgent.toLowerCase();\n                    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);\n                    setIsMobile(isMobileDevice || isSmallScreen);\n                }\n            }[\"MobileDetector.useEffect.handleResize\"];\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"MobileDetector.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"MobileDetector.useEffect\"];\n        }\n    }[\"MobileDetector.useEffect\"], []);\n    // Show loading spinner while detecting\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    // If not mobile, show desktop version\n    if (!isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Mobile version - render appropriate page component\n    const renderMobilePage = ()=>{\n        switch(pathname){\n            case '/':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileHomePage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, this);\n            case '/kitchens':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileKitchensPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 16\n                }, this);\n            case '/cabinets':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileCabinetsPage__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 16\n                }, this);\n            case '/about':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileAboutPage__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 16\n                }, this);\n            case '/contact':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileContactPage__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 16\n                }, this);\n            default:\n                // For admin pages or other routes, show desktop version\n                if (pathname.startsWith('/admin')) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: children\n                    }, void 0, false);\n                }\n                // For unknown routes, show mobile home page\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileHomePage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // If it's an admin route, don't use mobile layout\n    if (pathname.startsWith('/admin')) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: renderMobilePage()\n    }, void 0, false, {\n        fileName: \"/var/www/html/src/components/mobile/MobileDetector.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileDetector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileHomePage.tsx":
/*!**************************************************!*\
  !*** ./src/components/mobile/MobileHomePage.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocialMedia */ \"(ssr)/./src/hooks/useSocialMedia.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MobileHomePage() {\n    const { getWhatsAppLink, getPhoneNumber } = (0,_hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_3__.useSocialMedia)();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const heroSlides = [\n        {\n            title: 'مطابخ عصرية',\n            subtitle: 'تصاميم حديثة تناسب عصرنا',\n            image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',\n            gradient: 'from-kitchen-600 to-kitchen-800'\n        },\n        {\n            title: 'مطابخ كلاسيكية',\n            subtitle: 'أناقة تقليدية بلمسة عصرية',\n            image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',\n            gradient: 'from-warm-600 to-warm-800'\n        },\n        {\n            title: 'خزانات فاخرة',\n            subtitle: 'حلول تخزين ذكية ومميزة',\n            image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',\n            gradient: 'from-accent-600 to-accent-800'\n        }\n    ];\n    const features = [\n        {\n            icon: 'ri-award-line',\n            title: 'جودة عالية',\n            description: 'أفضل المواد والخامات'\n        },\n        {\n            icon: 'ri-time-line',\n            title: 'تسليم سريع',\n            description: 'التزام بالمواعيد المحددة'\n        },\n        {\n            icon: 'ri-customer-service-line',\n            title: 'خدمة مميزة',\n            description: 'دعم على مدار الساعة'\n        },\n        {\n            icon: 'ri-shield-check-line',\n            title: 'ضمان شامل',\n            description: 'ضمان على جميع المنتجات'\n        }\n    ];\n    const quickActions = [\n        {\n            title: 'استشارة مجانية',\n            icon: 'ri-chat-3-line',\n            color: 'from-kitchen-500 to-kitchen-600',\n            action: ()=>window.open(getWhatsAppLink(), '_blank')\n        },\n        {\n            title: 'اتصل بنا',\n            icon: 'ri-phone-line',\n            color: 'from-warm-500 to-warm-600',\n            action: ()=>window.open(`tel:${getPhoneNumber()}`, '_self')\n        }\n    ];\n    // Auto slide\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MobileHomePage.useEffect\": ()=>{\n            const timer = setInterval({\n                \"MobileHomePage.useEffect.timer\": ()=>{\n                    setCurrentSlide({\n                        \"MobileHomePage.useEffect.timer\": (prev)=>(prev + 1) % heroSlides.length\n                    }[\"MobileHomePage.useEffect.timer\"]);\n                }\n            }[\"MobileHomePage.useEffect.timer\"], 4000);\n            return ({\n                \"MobileHomePage.useEffect\": ()=>clearInterval(timer)\n            })[\"MobileHomePage.useEffect\"];\n        }\n    }[\"MobileHomePage.useEffect\"], [\n        heroSlides.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative h-[70vh] overflow-hidden\",\n                children: [\n                    heroSlides.map((slide, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `absolute inset-0 transition-transform duration-500 ease-in-out ${index === currentSlide ? 'translate-x-0' : 'translate-x-full'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `absolute inset-0 bg-gradient-to-br ${slide.gradient} opacity-90`\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-cover bg-center\",\n                                    style: {\n                                        backgroundImage: `url(${slide.image})`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 flex flex-col justify-center items-center h-full text-center px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white mb-3 animate-fade-in\",\n                                            children: slide.title\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-white/90 mb-6 animate-fade-in-delay\",\n                                            children: slide.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/contact\",\n                                            className: \"bg-white text-gray-900 px-6 py-3 rounded-full font-bold text-base hover:bg-gray-100 transition-all duration-300 shadow-xl transform hover:scale-105 animate-fade-in-delay-2\",\n                                            children: \"احصل على عرض سعر\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20\",\n                        children: heroSlides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentSlide(index),\n                                className: `w-3 h-3 rounded-full transition-all duration-300 ${index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'}`\n                            }, index, false, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-6 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 gap-3\",\n                    children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: action.action,\n                            className: `bg-gradient-to-r ${action.color} text-white p-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: `${action.icon} text-2xl mb-2 block`\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-sm\",\n                                    children: action.title\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-8 px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 text-center mb-6\",\n                        children: \"لماذا تختار عجائب الخبراء؟\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-xl shadow-lg text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gradient-to-br from-kitchen-500 to-kitchen-600 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: `${feature.icon} text-white text-lg`\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-gray-900 mb-2 text-sm\",\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-xs\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-8 px-4 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 text-center mb-6\",\n                        children: \"منتجاتنا\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/kitchens\",\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-32 bg-gradient-to-br from-kitchen-500 to-kitchen-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-restaurant-line text-2xl mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold\",\n                                                        children: \"المطابخ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 text-sm\",\n                                                        children: \"تصاميم عصرية وكلاسيكية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/cabinets\",\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-xl shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-32 bg-gradient-to-br from-accent-500 to-accent-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-archive-line text-2xl mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold\",\n                                                        children: \"الخزانات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/90 text-sm\",\n                                                        children: \"حلول تخزين ذكية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/src/components/mobile/MobileHomePage.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileHomePage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileKitchensPage.tsx":
/*!******************************************************!*\
  !*** ./src/components/mobile/MobileKitchensPage.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileKitchensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocialMedia */ \"(ssr)/./src/hooks/useSocialMedia.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MobileKitchensPage() {\n    const { getWhatsAppLink, getSocialMediaLink } = (0,_hooks_useSocialMedia__WEBPACK_IMPORTED_MODULE_2__.useSocialMedia)();\n    const [kitchens, setKitchens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedKitchen, setSelectedKitchen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MobileKitchensPage.useEffect\": ()=>{\n            fetchKitchens();\n        }\n    }[\"MobileKitchensPage.useEffect\"], []);\n    const fetchKitchens = async ()=>{\n        try {\n            const response = await fetch('/api/kitchens');\n            if (response.ok) {\n                const data = await response.json();\n                setKitchens(data);\n            }\n        } catch (error) {\n            console.error('Error fetching kitchens:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const openModal = (kitchen)=>{\n        setSelectedKitchen(kitchen);\n        setCurrentImageIndex(0);\n        document.body.style.overflow = 'hidden';\n    };\n    const closeModal = ()=>{\n        setSelectedKitchen(null);\n        setCurrentImageIndex(0);\n        document.body.style.overflow = 'unset';\n    };\n    const nextImage = ()=>{\n        if (selectedKitchen) {\n            setCurrentImageIndex((prev)=>prev === selectedKitchen.images.length - 1 ? 0 : prev + 1);\n        }\n    };\n    const prevImage = ()=>{\n        if (selectedKitchen) {\n            setCurrentImageIndex((prev)=>prev === 0 ? selectedKitchen.images.length - 1 : prev - 1);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري تحميل المطابخ...\"\n                    }, void 0, false, {\n                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-kitchen-600 to-kitchen-800 pt-8 pb-6 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"المطابخ\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-kitchen-100\",\n                            children: \"تصاميم عصرية وكلاسيكية مميزة\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: kitchens.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ri-restaurant-line text-4xl text-gray-400 mb-3\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-base\",\n                            children: \"لا توجد مطابخ متاحة حالياً\"\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: kitchens.map((kitchen)=>{\n                        const firstImage = kitchen.images && kitchen.images.length > 0 ? kitchen.images[0].image_url : 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop';\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\",\n                            onClick: ()=>openModal(kitchen),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: firstImage,\n                                            alt: kitchen.title,\n                                            className: \"w-full h-full object-cover transition-transform duration-300 hover:scale-105\",\n                                            onError: (e)=>{\n                                                e.currentTarget.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 bg-kitchen-600 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                            children: kitchen.category_name || 'مطبخ'\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 21\n                                        }, this),\n                                        kitchen.images && kitchen.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded-full text-xs\",\n                                            children: [\n                                                \"+\",\n                                                kitchen.images.length - 1,\n                                                \" صور\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-2\",\n                                            children: kitchen.title\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-3 line-clamp-2 text-sm\",\n                                            children: kitchen.description\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full bg-kitchen-600 text-white py-2 rounded-lg font-medium hover:bg-kitchen-700 transition-colors duration-300 text-sm\",\n                                            children: \"عرض التفاصيل\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, kitchen.id, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            selectedKitchen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/90 z-50 flex items-end justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-t-2xl w-full h-[95vh] overflow-hidden modal-slide-up flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-gray-900\",\n                                    children: selectedKitchen.title\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    className: \"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"ri-close-line text-lg text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 overflow-hidden\",\n                                    children: selectedKitchen.images && selectedKitchen.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedKitchen.images[currentImageIndex]?.image_url || 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',\n                                        alt: selectedKitchen.title,\n                                        className: \"w-full h-full object-cover\",\n                                        onError: (e)=>{\n                                            e.currentTarget.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop';\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop\",\n                                        alt: selectedKitchen.title,\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this),\n                                selectedKitchen.images && selectedKitchen.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-arrow-left-s-line text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-arrow-right-s-line text-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1\",\n                                            children: selectedKitchen.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setCurrentImageIndex(index),\n                                                    className: `w-2 h-2 rounded-full transition-all duration-300 ${index === currentImageIndex ? 'bg-white scale-125' : 'bg-white/50'}`\n                                                }, index, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-kitchen-100 text-kitchen-800 px-3 py-1 rounded-full text-sm font-medium\",\n                                        children: selectedKitchen.category_name || 'مطبخ'\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: selectedKitchen.description\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-4\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-award-line text-kitchen-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"جودة عالية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-time-line text-kitchen-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"تسليم سريع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-shield-check-line text-kitchen-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"ضمان شامل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-3 rounded-lg text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"ri-customer-service-line text-kitchen-600 text-xl mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: \"خدمة مميزة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-gray-900 mb-4 text-center\",\n                                            children: \"تابعنا على\"\n                                        }, void 0, false, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('snapchat'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white hover:bg-yellow-600 transition-colors duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-snapchat-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('instagram'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-instagram-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('tiktok'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-tiktok-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: getSocialMediaLink('twitter'),\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"w-12 h-12 bg-black rounded-full flex items-center justify-center text-white hover:bg-gray-800 transition-colors duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-twitter-x-line text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t bg-white\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.open(getWhatsAppLink(), '_blank'),\n                                        className: \"w-full bg-green-500 text-white py-3 rounded-xl font-medium hover:bg-green-600 transition-colors duration-300 flex items-center justify-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"ri-whatsapp-line text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"تواصل عبر واتساب\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: closeModal,\n                                        className: \"w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors duration-300\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/src/components/mobile/MobileKitchensPage.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileKitchensPage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileLayout.tsx":
/*!************************************************!*\
  !*** ./src/components/mobile/MobileLayout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction MobileLayout({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Remove scroll hiding functionality - keep navbar always visible\n    const isVisible = true;\n    const navItems = [\n        {\n            href: '/',\n            icon: 'ri-home-5-line',\n            activeIcon: 'ri-home-5-fill',\n            label: 'الرئيسية'\n        },\n        {\n            href: '/kitchens',\n            icon: 'ri-restaurant-line',\n            activeIcon: 'ri-restaurant-fill',\n            label: 'المطابخ'\n        },\n        {\n            href: '/cabinets',\n            icon: 'ri-archive-line',\n            activeIcon: 'ri-archive-fill',\n            label: 'الخزانات'\n        },\n        {\n            href: '/about',\n            icon: 'ri-information-line',\n            activeIcon: 'ri-information-fill',\n            label: 'من نحن'\n        },\n        {\n            href: '/contact',\n            icon: 'ri-phone-line',\n            activeIcon: 'ri-phone-fill',\n            label: 'تواصل معنا'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pb-20\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 bottom-nav-fixed\",\n                style: {\n                    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',\n                    backdropFilter: 'blur(10px)',\n                    WebkitBackdropFilter: 'blur(10px)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-around py-2 px-4 max-w-md mx-auto\",\n                    children: navItems.map((item)=>{\n                        const isActive = pathname === item.href;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: `flex flex-col items-center justify-center py-2 px-3 rounded-xl transition-all duration-300 min-w-0 flex-1 ${isActive ? 'text-kitchen-600 bg-kitchen-50' : 'text-gray-500 hover:text-kitchen-600 hover:bg-gray-50'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: `${isActive ? item.activeIcon : item.icon} text-xl mb-1 transition-all duration-300 ${isActive ? 'scale-110' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-xs font-medium transition-all duration-300 ${isActive ? 'text-kitchen-600' : 'text-gray-500'}`,\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this),\n                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 w-1 h-1 bg-kitchen-600 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, item.href, true, {\n                            fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/var/www/html/src/components/mobile/MobileLayout.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSocialMedia.ts":
/*!*************************************!*\
  !*** ./src/hooks/useSocialMedia.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSocialMedia: () => (/* binding */ useSocialMedia)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useSocialMedia auto */ \nconst useSocialMedia = ()=>{\n    const [socialMedia, setSocialMedia] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [contactInfo, setContactInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocialMedia.useEffect\": ()=>{\n            const fetchSocialMedia = {\n                \"useSocialMedia.useEffect.fetchSocialMedia\": async ()=>{\n                    try {\n                        const response = await fetch('/api/footer');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setSocialMedia(data.socialMedia || []);\n                            setContactInfo(data.contactInfo || []);\n                        }\n                    } catch (error) {\n                        console.error('Error fetching social media data:', error);\n                        // Fallback data\n                        setSocialMedia([\n                            {\n                                id: 1,\n                                platform: 'واتساب',\n                                url: 'https://wa.me/966557611105',\n                                icon: 'ri-whatsapp-line'\n                            },\n                            {\n                                id: 2,\n                                platform: 'تويتر',\n                                url: 'https://twitter.com',\n                                icon: 'ri-twitter-x-line'\n                            },\n                            {\n                                id: 3,\n                                platform: 'إنستغرام',\n                                url: 'https://instagram.com',\n                                icon: 'ri-instagram-line'\n                            },\n                            {\n                                id: 4,\n                                platform: 'فيسبوك',\n                                url: 'https://facebook.com',\n                                icon: 'ri-facebook-line'\n                            },\n                            {\n                                id: 5,\n                                platform: 'سناب شات',\n                                url: 'https://snapchat.com',\n                                icon: 'ri-snapchat-line'\n                            },\n                            {\n                                id: 6,\n                                platform: 'تيك توك',\n                                url: 'https://tiktok.com',\n                                icon: 'ri-tiktok-line'\n                            }\n                        ]);\n                        setContactInfo([\n                            {\n                                id: 1,\n                                icon: 'ri-phone-line',\n                                text: '+966 55 761 1105',\n                                type: 'phone'\n                            }\n                        ]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useSocialMedia.useEffect.fetchSocialMedia\"];\n            fetchSocialMedia();\n        }\n    }[\"useSocialMedia.useEffect\"], []);\n    // Helper functions to get specific social media links\n    const getWhatsAppLink = (message)=>{\n        const whatsapp = socialMedia.find((social)=>social.platform.toLowerCase().includes('واتساب') || social.platform.toLowerCase().includes('whatsapp'));\n        if (whatsapp) {\n            const baseUrl = whatsapp.url.includes('wa.me') ? whatsapp.url : `https://wa.me/966557611105`;\n            return message ? `${baseUrl}?text=${encodeURIComponent(message)}` : baseUrl;\n        }\n        return `https://wa.me/966557611105${message ? `?text=${encodeURIComponent(message)}` : ''}`;\n    };\n    const getPhoneNumber = ()=>{\n        const phone = contactInfo.find((contact)=>contact.type === 'phone');\n        return phone ? phone.text.replace(/\\s/g, '') : '+966557611105';\n    };\n    const getSocialMediaLink = (platform)=>{\n        const platformLower = platform.toLowerCase();\n        const social = socialMedia.find((s)=>{\n            const sPlatformLower = s.platform.toLowerCase();\n            return sPlatformLower.includes(platformLower) || platformLower.includes(sPlatformLower) || platformLower.includes('تويتر') && sPlatformLower.includes('twitter') || platformLower.includes('twitter') && sPlatformLower.includes('تويتر') || platformLower.includes('إنستغرام') && sPlatformLower.includes('instagram') || platformLower.includes('instagram') && sPlatformLower.includes('إنستغرام') || platformLower.includes('فيسبوك') && sPlatformLower.includes('facebook') || platformLower.includes('facebook') && sPlatformLower.includes('فيسبوك') || platformLower.includes('سناب') && sPlatformLower.includes('snap') || platformLower.includes('snap') && sPlatformLower.includes('سناب') || platformLower.includes('تيك') && sPlatformLower.includes('tik') || platformLower.includes('tik') && sPlatformLower.includes('تيك');\n        });\n        return social?.url || '#';\n    };\n    const getSocialMediaIcon = (platform)=>{\n        const platformLower = platform.toLowerCase();\n        const social = socialMedia.find((s)=>{\n            const sPlatformLower = s.platform.toLowerCase();\n            return sPlatformLower.includes(platformLower) || platformLower.includes(sPlatformLower) || platformLower.includes('تويتر') && sPlatformLower.includes('twitter') || platformLower.includes('twitter') && sPlatformLower.includes('تويتر') || platformLower.includes('إنستغرام') && sPlatformLower.includes('instagram') || platformLower.includes('instagram') && sPlatformLower.includes('إنستغرام') || platformLower.includes('فيسبوك') && sPlatformLower.includes('facebook') || platformLower.includes('facebook') && sPlatformLower.includes('فيسبوك') || platformLower.includes('سناب') && sPlatformLower.includes('snap') || platformLower.includes('snap') && sPlatformLower.includes('سناب') || platformLower.includes('تيك') && sPlatformLower.includes('tik') || platformLower.includes('tik') && sPlatformLower.includes('تيك');\n        });\n        return social?.icon || 'ri-link';\n    };\n    return {\n        socialMedia,\n        contactInfo,\n        loading,\n        getWhatsAppLink,\n        getPhoneNumber,\n        getSocialMediaLink,\n        getSocialMediaIcon\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSocialMedia.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=%2Fvar%2Fwww%2Fhtml%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fvar%2Fwww%2Fhtml&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();