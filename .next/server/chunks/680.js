"use strict";exports.id=680,exports.ids=[680],exports.modules={284:(e,t,l)=>{l.d(t,{X:()=>s});var i=l(3210);let s=()=>{let[e,t]=(0,i.useState)([]),[l,s]=(0,i.useState)([]),[n,r]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/footer");if(e.ok){let l=await e.json();t(l.socialMedia||[]),s(l.contactInfo||[])}}catch(e){console.error("Error fetching social media data:",e),t([{id:1,platform:"واتساب",url:"https://wa.me/966557611105",icon:"ri-whatsapp-line"},{id:2,platform:"تويتر",url:"https://twitter.com",icon:"ri-twitter-x-line"},{id:3,platform:"إنستغرام",url:"https://instagram.com",icon:"ri-instagram-line"},{id:4,platform:"فيسبوك",url:"https://facebook.com",icon:"ri-facebook-line"},{id:5,platform:"سناب شات",url:"https://snapchat.com",icon:"ri-snapchat-line"},{id:6,platform:"تيك توك",url:"https://tiktok.com",icon:"ri-tiktok-line"}]),s([{id:1,icon:"ri-phone-line",text:"+966 55 761 1105",type:"phone"}])}finally{r(!1)}})()},[]),{socialMedia:e,contactInfo:l,loading:n,getWhatsAppLink:t=>{let l=e.find(e=>e.platform.toLowerCase().includes("واتساب")||e.platform.toLowerCase().includes("whatsapp"));if(l){let e=l.url.includes("wa.me")?l.url:"https://wa.me/966557611105";return t?`${e}?text=${encodeURIComponent(t)}`:e}return`https://wa.me/966557611105${t?`?text=${encodeURIComponent(t)}`:""}`},getPhoneNumber:()=>{let e=l.find(e=>"phone"===e.type);return e?e.text.replace(/\s/g,""):"+966557611105"},getSocialMediaLink:t=>{let l=t.toLowerCase(),i=e.find(e=>{let t=e.platform.toLowerCase();return t.includes(l)||l.includes(t)||l.includes("تويتر")&&t.includes("twitter")||l.includes("twitter")&&t.includes("تويتر")||l.includes("إنستغرام")&&t.includes("instagram")||l.includes("instagram")&&t.includes("إنستغرام")||l.includes("فيسبوك")&&t.includes("facebook")||l.includes("facebook")&&t.includes("فيسبوك")||l.includes("سناب")&&t.includes("snap")||l.includes("snap")&&t.includes("سناب")||l.includes("تيك")&&t.includes("tik")||l.includes("tik")&&t.includes("تيك")});return i?.url||"#"},getSocialMediaIcon:t=>{let l=t.toLowerCase(),i=e.find(e=>{let t=e.platform.toLowerCase();return t.includes(l)||l.includes(t)||l.includes("تويتر")&&t.includes("twitter")||l.includes("twitter")&&t.includes("تويتر")||l.includes("إنستغرام")&&t.includes("instagram")||l.includes("instagram")&&t.includes("إنستغرام")||l.includes("فيسبوك")&&t.includes("facebook")||l.includes("facebook")&&t.includes("فيسبوك")||l.includes("سناب")&&t.includes("snap")||l.includes("snap")&&t.includes("سناب")||l.includes("تيك")&&t.includes("tik")||l.includes("tik")&&t.includes("تيك")});return i?.icon||"ri-link"}}}},6680:(e,t,l)=>{l.d(t,{A:()=>d});var i=l(687),s=l(3210),n=l(8920),r=l(6001),a=l(2036),c=l(3372),o=l(284);l(2822),l(5866),l(4120);let d=({product:e,onClose:t,type:l})=>{let{socialMedia:d,getWhatsAppLink:u,getPhoneNumber:m,getSocialMediaLink:h,getSocialMediaIcon:p}=(0,o.X)();(0,s.useEffect)(()=>{document.body.style.overflow="hidden";let e=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",e),()=>{document.body.style.overflow="unset",document.removeEventListener("keydown",e)}},[t]);let x=(()=>{let t=[],i=`مرحباً، أريد الاستفسار عن ${e.title}`;t.push({name:"واتساب",icon:p("واتساب")||"ri-whatsapp-line",url:u(i),color:"bg-green-500 hover:bg-green-600"});let s=`شاهد هذا ${"kitchen"===l?"المطبخ":"الخزانة"} الرائع من عجائب الخبراء: ${e.title}`,n=h("تويتر");n&&"#"!==n&&t.push({name:"تويتر",icon:p("تويتر")||"ri-twitter-x-line",url:n.includes("twitter.com")?`https://twitter.com/intent/tweet?text=${encodeURIComponent(s)}`:n,color:"bg-blue-500 hover:bg-blue-600"});let r=h("فيسبوك");r&&"#"!==r&&t.push({name:"فيسبوك",icon:p("فيسبوك")||"ri-facebook-line",url:r.includes("facebook.com")?`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`:r,color:"bg-blue-600 hover:bg-blue-700"});let a=h("إنستغرام");a&&"#"!==a&&t.push({name:"إنستغرام",icon:p("إنستغرام")||"ri-instagram-line",url:a,color:"bg-pink-500 hover:bg-pink-600"});let c=h("سناب");c&&"#"!==c&&t.push({name:"سناب شات",icon:p("سناب")||"ri-snapchat-line",url:c,color:"bg-yellow-500 hover:bg-yellow-600"});let o=h("تيك توك");return o&&"#"!==o&&t.push({name:"تيك توك",icon:p("تيك توك")||"ri-tiktok-line",url:o,color:"bg-black hover:bg-gray-800"}),t})();return(0,i.jsx)(n.N,{children:(0,i.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,i.jsx)(r.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/70 backdrop-blur-sm",onClick:t}),(0,i.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,i.jsxs)(r.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden",onClick:e=>e.stopPropagation(),children:[(0,i.jsx)("button",{onClick:t,className:"absolute top-4 left-4 z-10 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-colors",children:(0,i.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 h-full",children:[(0,i.jsxs)("div",{className:"relative h-64 lg:h-full",children:[e.images&&e.images.length>0?(0,i.jsx)(a.RC,{modules:[c.Vx,c.dK,c.Ij],navigation:!0,pagination:{clickable:!0},autoplay:{delay:4e3,disableOnInteraction:!1},className:"h-full",children:e.images.map(t=>(0,i.jsx)(a.qr,{children:(0,i.jsx)("img",{src:t.image_url,alt:t.alt_text||e.title,className:"w-full h-full object-cover"})},t.id))}):(0,i.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-500 text-lg",children:"لا توجد صور"})}),e.category_name&&(0,i.jsx)("div",{className:`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-medium text-white ${"kitchen"===l?"bg-primary-500":"bg-secondary-500"}`,children:e.category_name})]}),(0,i.jsxs)("div",{className:"p-6 lg:p-8 flex flex-col justify-between overflow-y-auto",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-4",children:e.title}),e.description&&(0,i.jsx)("p",{className:"text-gray-600 leading-relaxed mb-6 text-lg",children:e.description}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"المميزات:"}),(0,i.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"تصميم عصري وأنيق"]}),(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"خامات عالية الجودة"]}),(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"ضمان شامل"]}),(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-primary-500 rounded-full ml-3"}),"تركيب احترافي"]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,i.jsxs)("a",{href:u(`مرحباً، أريد الاستفسار عن ${e.title}`),target:"_blank",rel:"noopener noreferrer",className:"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-bold text-center transition-colors flex items-center justify-center gap-2",children:[(0,i.jsx)("i",{className:"ri-whatsapp-line text-lg"}),"واتساب"]}),(0,i.jsxs)("a",{href:`tel:${m()}`,className:`${"kitchen"===l?"bg-primary-500 hover:bg-primary-600":"bg-secondary-500 hover:bg-secondary-600"} text-white px-6 py-3 rounded-full font-bold text-center transition-colors flex items-center justify-center gap-2`,children:[(0,i.jsx)("i",{className:"ri-phone-line text-lg"}),"اتصل بنا"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"شارك:"}),(0,i.jsx)("div",{className:"flex gap-3",children:x.map(e=>(0,i.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:`w-10 h-10 ${e.color} text-white rounded-full flex items-center justify-center transition-colors`,title:e.name,children:(0,i.jsx)("i",{className:`${e.icon} text-sm`})},e.name))})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 border-t pt-4",children:[(0,i.jsx)("p",{children:"\uD83D\uDCA1 احصل على استشارة مجانية وعرض سعر مخصص"}),(0,i.jsx)("p",{children:"\uD83D\uDE9A توصيل وتركيب مجاني داخل الرياض"}),(0,i.jsx)("p",{children:"\uD83D\uDEE1️ ضمان شامل لمدة سنتين"})]})]})]})]})]})})]})})}}};