(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},671:(a,e,l)=>{Promise.resolve().then(l.t.bind(l,5865,23)),Promise.resolve().then(l.t.bind(l,347,23))},5865:a=>{a.exports={style:{fontFamily:"'Tajawal', 'Tajawal Fallback'",fontStyle:"normal"},className:"__className_76817d",variable:"__variable_76817d"}}},a=>{var e=e=>a(a.s=e);a.O(0,[100,84,690,441,684,358],()=>e(671)),_N_E=a.O()}]);