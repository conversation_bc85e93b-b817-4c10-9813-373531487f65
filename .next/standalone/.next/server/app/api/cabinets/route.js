(()=>{var e={};e.id=381,e.ids=[381],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3564:(e,t,r)=>{"use strict";r.d(t,{Mc:()=>d,Xb:()=>o,Yp:()=>u});let s=require("sqlite3");var a=r.n(s),n=r(3873),i=r.n(n);class c{constructor(){this.db=null}static getInstance(){return c.instance||(c.instance=new c),c.instance}async connect(){if(this.db)return this.db;let e=i().join(process.cwd(),"database","khobra_kitchens.db");return new Promise((t,r)=>{this.db=new(a()).Database(e,e=>{e?(console.error("Error opening database:",e.message),r(e)):(console.log("✅ Connected to SQLite database"),t(this.db))})})}async close(){if(this.db)return new Promise((e,t)=>{this.db.close(r=>{r?t(r):(this.db=null,e())})})}getDatabase(){return this.db}}let o=(e,t=[])=>new Promise(async(r,s)=>{try{(await c.getInstance().connect()).get(e,t,(e,t)=>{e?s(e):r(t)})}catch(e){s(e)}}),d=(e,t=[])=>new Promise(async(r,s)=>{try{(await c.getInstance().connect()).all(e,t,(e,t)=>{e?s(e):r(t||[])})}catch(e){s(e)}}),u=(e,t=[])=>new Promise(async(r,s)=>{try{(await c.getInstance().connect()).run(e,t,function(e){e?s(e):r(this)})}catch(e){s(e)}})},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>b});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>u});var a=r(6559),n=r(8088),i=r(7719),c=r(2190),o=r(3564);async function d(){try{let e=await (0,o.Mc)(`
      SELECT c.*, cat.name as category_name, cat.slug as category_slug
      FROM cabinets c
      LEFT JOIN categories cat ON c.category_id = cat.id
      WHERE c.is_active = 1
      ORDER BY c.sort_order, c.id
    `);for(let t of e)t.images=await (0,o.Mc)("SELECT * FROM cabinet_images WHERE cabinet_id = ? ORDER BY sort_order",[t.id]);return c.NextResponse.json(e)}catch(e){return console.error("Error fetching cabinets:",e),c.NextResponse.json({error:"Failed to fetch cabinets"},{status:500})}}async function u(e){try{let{title:t,description:r,category_id:s,is_featured:a=0,sort_order:n=0}=await e.json();if(!t)return c.NextResponse.json({error:"Title is required"},{status:400});let i=await (0,o.Yp)(`INSERT INTO cabinets (title, description, category_id, is_featured, sort_order)
       VALUES (?, ?, ?, ?, ?)`,[t,r,s,a,n]);return c.NextResponse.json({id:i.lastID,title:t,description:r,category_id:s,is_featured:a,sort_order:n,message:"Cabinet added successfully"})}catch(e){return console.error("Error adding cabinet:",e),c.NextResponse.json({error:"Failed to add cabinet"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cabinets/route",pathname:"/api/cabinets",filename:"route",bundlePath:"app/api/cabinets/route"},resolvedPagePath:"/var/www/html/src/app/api/cabinets/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:b,serverHooks:g}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:b})}},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(6483));module.exports=s})();