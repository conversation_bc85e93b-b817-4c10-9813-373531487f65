(()=>{var e={};e.id=126,e.ids=[126],e.modules={418:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/var/www/html/src/app/cabinets/CabinetsPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/var/www/html/src/app/cabinets/CabinetsPageContent.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1544:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(687),r=s(3210),i=s(6001),n=s(2036),l=s(3372);s(2822),s(5866),s(4120);var o=s(9190),d=s(1317),c=s(6680);let x=()=>{let[e,t]=(0,r.useState)([]),[s,x]=(0,r.useState)([]),[p,m]=(0,r.useState)("all"),[u,h]=(0,r.useState)(null),[g,b]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([fetch("/api/cabinets"),fetch("/api/categories?type=cabinet")]);if(e.ok){let s=await e.json();t(s)}if(s.ok){let e=await s.json();x(e)}}catch(e){console.error("Error fetching data:",e)}finally{b(!1)}})()},[]);let f="all"===p?e:e.filter(e=>e.category_slug===p);return g?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",dir:"rtl",children:[(0,a.jsx)(o.default,{}),(0,a.jsx)("div",{className:"pt-20 flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-secondary-500"})}),(0,a.jsx)(d.default,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",dir:"rtl",children:[(0,a.jsx)(o.default,{}),(0,a.jsx)("section",{className:"pt-20 pb-16 bg-gradient-to-br from-secondary-600 to-secondary-800",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center text-white",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-6",children:"معرض الخزانات"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 max-w-3xl mx-auto",children:"تصفح مجموعتنا الرائعة من الخزانات المصممة خصيصاً لتناسب احتياجاتك وتضفي لمسة أناقة على منزلك"}),(0,a.jsxs)("div",{className:"text-lg opacity-90",children:[e.length," خزانة متاحة"]})]})})}),s.length>0&&(0,a.jsx)("section",{className:"py-8 bg-white border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,a.jsx)("button",{onClick:()=>m("all"),className:`px-6 py-3 rounded-full font-medium transition-all duration-300 ${"all"===p?"bg-secondary-500 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"جميع الخزانات"}),s.map(e=>(0,a.jsx)("button",{onClick:()=>m(e.slug),className:`px-6 py-3 rounded-full font-medium transition-all duration-300 ${p===e.slug?"bg-secondary-500 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e.name},e.id))]})})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:f.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",children:f.map((e,t)=>(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},className:"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer",onClick:()=>h(e),children:[(0,a.jsxs)("div",{className:"relative h-64 overflow-hidden",children:[e.images&&e.images.length>0?(0,a.jsx)(n.RC,{modules:[l.Vx,l.dK,l.Ij],navigation:!0,pagination:{clickable:!0},autoplay:{delay:3e3,disableOnInteraction:!1},className:"h-full",children:e.images.map(t=>(0,a.jsx)(n.qr,{children:(0,a.jsx)("img",{src:t.image_url,alt:t.alt_text||e.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"})},t.id))}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-lg",children:"لا توجد صور"})}),e.category_name&&(0,a.jsx)("div",{className:"absolute top-4 right-4 bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-medium",children:e.category_name}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",children:(0,a.jsx)("button",{className:"bg-white text-secondary-600 px-6 py-3 rounded-full font-bold hover:bg-gray-100 transition-colors",children:"عرض التفاصيل"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 group-hover:text-secondary-600 transition-colors",children:e.title}),e.description&&(0,a.jsx)("p",{className:"text-gray-600 line-clamp-2 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.images?.length||0," صورة"]}),(0,a.jsx)("span",{className:"text-secondary-500 font-medium",children:"عرض المزيد ←"})]})]})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"لا توجد خزانات في هذه الفئة"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"جرب تصفح فئة أخرى أو عرض جميع الخزانات"}),(0,a.jsx)("button",{onClick:()=>m("all"),className:"bg-secondary-500 text-white px-8 py-3 rounded-full font-bold hover:bg-secondary-600 transition-colors",children:"عرض جميع الخزانات"})]})})}),u&&(0,a.jsx)(c.A,{product:u,onClose:()=>h(null),type:"cabinet"}),(0,a.jsx)(d.default,{})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5218:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>i});var a=s(7413),r=s(418);let i={title:"الخزانات - عجائب الخبراء",description:"تصفح مجموعتنا الرائعة من الخزانات المصممة خصيصاً لتناسب احتياجاتك وتضفي لمسة أناقة على منزلك في المملكة العربية السعودية.",keywords:["خزانات السعودية","خزانات ملابس","خزانات عصرية","خزانات كلاسيكية","تصميم خزانات","عجائب الخبراء"],alternates:{canonical:"/cabinets"},openGraph:{title:"الخزانات - عجائب الخبراء",description:"تصفح مجموعتنا الرائعة من الخزانات المصممة خصيصاً لتناسب احتياجاتك وتضفي لمسة أناقة على منزلك في المملكة العربية السعودية.",url:"https://khobrakitchens.com/cabinets",type:"website"}};function n(){return(0,a.jsx)(r.default,{})}},5428:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(5239),r=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["cabinets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5218)),"/var/www/html/src/app/cabinets/page.tsx"]}]},{metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/var/www/html/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["/var/www/html/src/app/cabinets/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cabinets/page",pathname:"/cabinets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6569:(e,t,s)=>{Promise.resolve().then(s.bind(s,418))},6841:(e,t,s)=>{Promise.resolve().then(s.bind(s,1544))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,425,866,426,429,762,680],()=>s(5428));module.exports=a})();